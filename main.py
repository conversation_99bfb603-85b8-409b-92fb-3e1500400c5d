import pandas as pd
import re
import os
import sys
import tkinter as tk
from tkinter import Toplevel, Entry, Label, Button, messagebox
from tkinter import filedialog

# ==============================================================================
# ČÁST 0: Dialogové okno pro zadání chybějíc<PERSON>ch dat
# ==============================================================================

class InputDialog(Toplevel):
    def __init__(self, parent, title=None):
        super().__init__(parent)
        self.parent = parent
        self.transient(parent)
        if title: self.title(title)
        self.result = None
        self.body = tk.Frame(self)
        self.initial_focus = self.body_contents(self.body)
        self.body.pack(padx=5, pady=5)
        self.buttonbox()
        self.grab_set()
        if not self.initial_focus: self.initial_focus = self
        self.protocol("WM_DELETE_WINDOW", self.cancel)
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        self.initial_focus.focus_set()
        self.wait_window(self)

    def body_contents(self, master):
        Label(master, text="Datum dodání (DD.MM.RRRR):").grid(row=0, sticky="w")
        Label(master, text="Číslo zákazníka:").grid(row=1, sticky="w")
        self.entry_date = Entry(master)
        self.entry_customer = Entry(master)
        self.entry_date.insert(0, "06.06.2025")
        self.entry_customer.insert(0, "25555308")
        self.entry_date.grid(row=0, column=1)
        self.entry_customer.grid(row=1, column=1)
        return self.entry_date

    def buttonbox(self):
        box = tk.Frame(self)
        Button(box, text="OK", width=10, command=self.ok, default=tk.ACTIVE).pack(side=tk.LEFT, padx=5, pady=5)
        Button(box, text="Zrušit", width=10, command=self.cancel).pack(side=tk.LEFT, padx=5, pady=5)
        self.bind("<Return>", self.ok)
        self.bind("<Escape>", self.cancel)
        box.pack()

    def ok(self, event=None):
        self.result = (self.entry_date.get(), self.entry_customer.get())
        self.withdraw()
        self.update_idletasks()
        self.parent.focus_set()
        self.destroy()

    def cancel(self, event=None):
        self.parent.focus_set()
        self.destroy()

# ==============================================================================
# ČÁST 1: ZPRACOVÁNÍ DAT (JÁDRO PROGRAMU)
# ==============================================================================

def process_labara_order(order_path, catalog_path, dated_input, customer_no_input):
    """Zpracuje objednávku Labara a v případě více shod vybere první nalezenou."""
    print(f"--- Zpracovávám objednávku Labara: {os.path.basename(order_path)} ---")
    try:
        order_items_df = pd.read_csv(order_path, sep=';', encoding='windows-1250')
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat. Zkontrolujte soubory.\n\nDetail chyby: {e}")

    order_items_df.rename(columns={'číslo zboží': 'cislo_zbozi', 'množství': 'mnozstvi', 'název': 'nazev'}, inplace=True)
    order_items_df = order_items_df.dropna(subset=['cislo_zbozi'])
    
    # Přidání unikátního identifikátoru pro každý řádek objednávky
    order_items_df.reset_index(inplace=True)

    def get_robust_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'přířez\s+([\d,]+)', text, re.IGNORECASE)
        if not thickness_match: # Zkusíme jiný formát, např. "přířez 15 mm"
            thickness_match = re.search(r'přířez\s+([\d,]+)\s*mm', text, re.IGNORECASE)
            
        if material_num_match and thickness_match:
            material_num = material_num_match.group(1)
            thickness = float(thickness_match.group(1).replace(',', '.'))
            return f"{material_num}-{thickness:.2f}"
        return None
    
    def get_catalog_key(text):
        text = str(text)
        material_num_match = re.search(r'(\d{4})', text)
        thickness_match = re.search(r'([\d,]+\.\d{2})', text.replace(',', '.'))
        if material_num_match and thickness_match:
            material_num = material_num_match.group(1)
            thickness = thickness_match.group(1)
            return f"{material_num}-{thickness}"
        return None

    order_items_df['robust_key'] = order_items_df['nazev'].apply(get_robust_key)
    alde_catalog_df['robust_key'] = alde_catalog_df['DESCRIPTION'].apply(get_catalog_key)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO'}, inplace=True)
    
    # Propojení, které může vytvořit duplicity, pokud je více shod
    final_df = pd.merge(order_items_df, alde_catalog_df, on='robust_key', how='left')

    # --- ZMĚNA ZDE: VÝBĚR PRVNÍ MOŽNOSTI ---
    # Odstranění duplicit vzniklých při spojení, ponechá se jen první nalezená shoda pro každou položku.
    final_df.drop_duplicates(subset=['index'], keep='first', inplace=True)

    unmatched = final_df[final_df['CATALOG_NO'].isnull()]
    if not unmatched.empty:
        messagebox.showwarning("Pozor", "Pro následující položky nebyla nalezena shoda a nebudou ve výstupu:\n\n" + "\n".join(unmatched['nazev']))

    customer_po_no = os.path.splitext(os.path.basename(order_path))[0]
    final_df['mnozstvi'] = final_df['mnozstvi'].astype(str).str.replace(' ks', '', regex=False).astype(int)

    def parse_dimensions(nazev):
        nazev = str(nazev)
        match_dims = re.search(r'-\s*([\d,\.]+)\s*[-x]\s*([\d,\.]+)', nazev, re.IGNORECASE)
        if match_dims:
            return match_dims.group(1).strip().replace(',', '.'), match_dims.group(2).strip().replace(',', '.')
        return None, None

    dims_data = final_df['nazev'].apply(lambda x: pd.Series(parse_dimensions(x)))
    final_df[['W', 'L']] = dims_data

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': customer_no_input,
        'CATALOG_NO': final_df['CATALOG_NO'], 'QTY': final_df['mnozstvi'],
        'DATED': pd.to_datetime(dated_input, format='%d.%m.%Y').strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK',
        'CUSTOMER_PO_NO': customer_po_no, 'W': final_df['W'], 'L': final_df['L']
    })
    
    output_df.dropna(subset=['CATALOG_NO'], inplace=True)

    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_folder = os.path.join(os.path.expanduser("~"), "Documents", "Migrace_Vystupy")
    if not os.path.exists(output_folder): os.makedirs(output_folder)
    output_filename = f"vystup_{customer_po_no}.csv"
    output_path = os.path.join(output_folder, output_filename)
    output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
    return output_path


def process_descon_order(order_path, catalog_path):
    # ... kód pro Descon beze změny ...
    print(f"--- Zpracovávám objednávku Descon: {os.path.basename(order_path)} ---")
    try:
        # Nejprve zkontrolujeme, kolik listů má soubor
        excel_file = pd.ExcelFile(order_path)
        sheet_names = excel_file.sheet_names
        print(f"Nalezené listy: {sheet_names}")

        # Pokud má soubor více než 1 list, použijeme index 1, jinak 0
        sheet_index = 1 if len(sheet_names) > 1 else 0
        print(f"Používám list s indexem: {sheet_index} ({sheet_names[sheet_index]})")

        order_items_df = pd.read_excel(order_path, sheet_name=sheet_index, header=0, skipfooter=3)
        alde_catalog_df = pd.read_excel(catalog_path, sheet_name=0)
    except Exception as e:
        raise ValueError(f"Chyba při načítání dat pro Descon.\n\nDetail chyby: {e}")

    order_items_df = order_items_df[order_items_df['CisloDilu'].str.contains('_', na=False)]
    typ_column_name = order_items_df.columns[3]
    date_column_name = order_items_df.columns[-1]

    def create_search_key(row):
        material = row['MaterialVyroba']; typ = row[typ_column_name]; thickness = row['Tloustka']
        base_material = ""
        if "5083" in material: base_material = "5083"
        elif "6082" in material: base_material = "ENAW6082T651"
        if "frézovaná" in str(typ): base_material += " litá frézovaná"
        elif "litá" in str(typ): base_material += " litá"
        if not base_material or pd.isna(thickness): return None
        return f"{base_material} {float(thickness):.2f}".replace('.', ',')

    order_items_df['search_key'] = order_items_df.apply(create_search_key, axis=1)
    alde_catalog_df.rename(columns={'PART_NO': 'CATALOG_NO', 'DESCRIPTION': 'search_key'}, inplace=True)
    final_df = pd.merge(order_items_df, alde_catalog_df[['CATALOG_NO', 'search_key']], on='search_key', how='left')

    output_df = pd.DataFrame({
        'CONTRACT': 'CZ21', 'CUSTOMER_NO': '505992', 'CATALOG_NO': final_df['CATALOG_NO'],
        'QTY': final_df['KS'], 'DATED': pd.to_datetime(final_df[date_column_name].iloc[0]).strftime('%d.%m.%Y'),
        'PRICE': 0, 'CURRENCY_CODE': 'CZK', 'CUSTOMER_PO_NO': final_df['Zakazka'],
        'W': final_df['SirkaPrumer'], 'L': final_df['Delka']
    })
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_folder = os.path.join(os.path.expanduser("~"), "Documents", "Migrace_Vystupy")
    if not os.path.exists(output_folder): os.makedirs(output_folder)
    output_path = os.path.join(output_folder, 'vystup_descon.csv')
    output_df.to_csv(output_path, index=False, sep=';', encoding='utf-8-sig')
    return output_path

# ==============================================================================
# ČÁST 2: GRAFICKÉ ROZHRANÍ (GUI)
# ==============================================================================

def select_and_process_labara(root):
    filepath = filedialog.askopenfilename(title="Vyberte CSV soubor s objednávkou LABARA", filetypes=[("CSV soubory", "*.csv")])
    if not filepath: return
    
    dialog = InputDialog(root, "Zadejte údaje z PDF")
    if dialog.result:
        dated, customer_no = dialog.result
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            catalog_path = os.path.join(script_dir, 'ALDE_katalog_položek.xlsx')
            if not os.path.exists(catalog_path):
                messagebox.showerror("Chyba souboru", "Katalogový soubor 'ALDE_katalog_položek.xlsx' nebyl nalezen.")
                return
            output_path = process_labara_order(filepath, catalog_path, dated, customer_no)
            messagebox.showinfo("Hotovo", f"Zpracování dokončeno!\n\nVýstup byl uložen do:\n{output_path}")
        except Exception as e:
            messagebox.showerror("Chyba při zpracování", f"Vyskytla se neočekávaná chyba:\n\n{e}")

def select_and_process_descon():
    filepath = filedialog.askopenfilename(title="Vyberte Excel soubor s objednávkou DESCON", filetypes=[("Excel soubory", "*.xlsx")])
    if not filepath: return
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        catalog_path = os.path.join(script_dir, 'ALDE_katalog_položek.xlsx')
        if not os.path.exists(catalog_path):
            messagebox.showerror("Chyba souboru", "Katalogový soubor 'ALDE_katalog_položek.xlsx' nebyl nalezen.")
            return
        output_path = process_descon_order(filepath, catalog_path)
        messagebox.showinfo("Hotovo", f"Zpracování dokončeno!\n\nVýstup byl uložen do:\n{output_path}")
    except Exception as e:
        messagebox.showerror("Chyba při zpracování", f"Vyskytla se neočekávaná chyba:\n\n{e}")

if __name__ == "__main__":
    root = tk.Tk()
    root.title("Nástroj pro migraci objednávek v7.1 (Pragmatické párování)")
    root.geometry("450x200")
    root.resizable(False, False)

    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(expand=True, fill=tk.BOTH)

    label = tk.Label(main_frame, text="Vyberte typ objednávky ke zpracování:", font=("Segoe UI", 12))
    label.pack(pady=(0, 15))

    btn_labara = tk.Button(main_frame, text="Zpracovat objednávku LABARA", 
                                 command=lambda: select_and_process_labara(root),
                                 width=40, height=2, bg='#D4EDDA')
    btn_labara.pack(pady=5)
    
    btn_descon = tk.Button(main_frame, text="Zpracovat objednávku DESCON", 
                           command=select_and_process_descon,
                           width=40, height=2)
    btn_descon.pack(pady=5)

    root.mainloop()