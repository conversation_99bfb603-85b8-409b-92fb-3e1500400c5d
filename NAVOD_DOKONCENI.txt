NÁVOD PRO DOKONČENÍ MIGRACE_OBJEDNAVEK.EXE
=====================================================

SITUACE:
- Byly provedeny všechny úpravy pro ukládání do složky Documents
- Připraveny soubory pro rebuild na Windows systému
- Linux systém má problém s encoding názvu složky "Migrace - Arnošt"

ŘEŠENÍ - SPUSTIT NA WINDOWS:
============================

1. ZKOPÍROVAT SOUBORY
   - Zkopírujte celou složku "Migrace - Arnošt" na Windows počítač
   - Zkopírujte také soubor "main_fixed.py" do stejného adresáře
   - Zkopírujte soubor "SPUSTIT_NA_WINDOWS.bat"

2. SPUSTIT REBUILD
   - Na Windows systému spusťte: SPUSTIT_NA_WINDOWS.bat
   - Skript automaticky najde složku projektu
   - Zkopíruje správný main.py s úpravami pro Documents
   - Aktivuje virtual environment
   - Nainstaluje závislosti
   - Vytvoří nový Migrace_Objednavek.exe

3. VÝSLEDEK
   - Nový exe bude v dist/ složce
   - Aplikace bude ukládat do: Documents/Migrace_Vystupy/
   - Katalog bude automaticky zkopírován

SOUBORY PRO WINDOWS:
==================
✓ SPUSTIT_NA_WINDOWS.bat  - Hlavní rebuild skript
✓ main_fixed.py           - Opraven kód s Documents podporou  
✓ Migrace - Arnošt/       - Projektová složka s venv a spec soubory

POTVRZENÍ ÚPRAV:
===============
✓ Funkce get_documents_folder() přidána
✓ Cesty změněny z vystup/ na Documents/Migrace_Vystupy/
✓ Cross-platform detekce Documents složky (Windows registry + fallback)
✓ Automatické vytvoření výstupní složky

LINUX DŮVOD SELHÁNÍ:
===================
- Název složky "Migrace - Arnošt" obsahuje čestký znak 'š' 
- Linux má problém s encoding při programové manipulaci
- Windows tento problém nemá

NÁSLEDUJÍCÍ KROKY:
================
1. Přeneste soubory na Windows
2. Spusťte SPUSTIT_NA_WINDOWS.bat
3. Použijte nový exe soubor z dist/ složky
4. Ověřte, že ukládá do Documents/Migrace_Vystupy/