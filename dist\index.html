<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zpraco<PERSON><PERSON> ob<PERSON>k a skladu - verze 11</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .main-layout {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }

        .content-area {
            flex: 2;
            min-width: 0;
        }

        .help-area {
            flex: 1;
            min-width: 300px;
            position: sticky;
            top: 20px;
        }
        .card {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"], input[type="text"] {
            display: block;
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #2980b9;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result-box {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .hidden {
            display: none;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: fixed; /* Fixní šířka sloupců */
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            white-space: nowrap; /* Zabránění zalamování textu */
            overflow: hidden; /* Skrytí přetékajícího textu */
            text-overflow: ellipsis; /* Zobrazení tří teček při přetečení */
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        /* Nastavení šířky sloupců pro 8 sloupců */
        table th:nth-child(1), table td:nth-child(1) { width: 20%; } /* Materiál */
        table th:nth-child(2), table td:nth-child(2) { width: 12%; } /* Množství na skladě CZ21 */
        table th:nth-child(3), table td:nth-child(3) { width: 12%; } /* Množství na skladě CZ23 */
        table th:nth-child(4), table td:nth-child(4) { width: 12%; } /* Množství na skladě SK11 */
        table th:nth-child(5), table td:nth-child(5) { width: 12%; } /* Požadované množství */
        table th:nth-child(6), table td:nth-child(6) { width: 10%; } /* CZ21 */
        table th:nth-child(7), table td:nth-child(7) { width: 10%; } /* CZ23 */
        table th:nth-child(8), table td:nth-child(8) { width: 10%; } /* SK11 */

        /* Přidání možnosti zobrazit celý obsah buňky při najetí myší */
        td:hover {
            overflow: visible;
            white-space: normal;
            word-wrap: break-word;
            background-color: #f0f0f0;
            position: relative;
            z-index: 1;
        }
        .location-available {
            color: #27ae60;
            font-weight: bold;
        }
        .location-unavailable {
            color: #e74c3c;
        }
        .location-reserved {
            color: #f39c12;
            font-weight: bold;
        }

        /* Styly pro nápovědu */
        .help-section {
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
        }
        .help-step {
            margin-bottom: 10px;
            padding: 8px;
            background-color: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .help-step h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
            font-size: 14px;
        }
        .help-step p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
        .help-step ul {
            margin: 5px 0 0 0;
            font-size: 11px;
            text-align: left;
            padding-left: 15px;
        }
        .help-symbols {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            margin-bottom: 10px;
        }
        .help-symbols h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 14px;
        }
        .symbol-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 12px;
        }
        .symbol {
            font-size: 16px;
            font-weight: bold;
            margin-right: 8px;
            min-width: 20px;
        }
        .symbol.available {
            color: #27ae60;
        }
        .symbol.reserved {
            color: #f39c12;
        }
        .symbol.unavailable {
            color: #e74c3c;
        }
        .help-tips {
            background-color: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .help-tips h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 14px;
        }
        .help-tips ul {
            margin: 0 0 0 15px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Zpracování objednávek a skladu</h1>

        <div class="main-layout">
            <!-- Hlavní obsah - 2/3 stránky -->
            <div class="content-area">
                <div class="card" id="warehouse-card">
            <h2>Krok 1: Nahrání stavu skladu</h2>
            <form id="warehouse-form">
                <div class="form-group">
                    <label for="warehouse-file">Vyberte CSV soubor se stavem skladu:</label>
                    <input type="file" id="warehouse-file" accept=".csv" required>
                </div>
                <button type="submit">Nahrát stav skladu</button>
            </form>
            <div id="warehouse-result" class="result-box hidden"></div>
        </div>

        <div class="card hidden" id="order-card">
            <h2>Krok 2: Nahrání objednávky</h2>
            <form id="order-form">
                <div class="form-group">
                    <label for="postal-code">PSČ zákazníka:</label>
                    <input type="text" id="postal-code" required pattern="[0-9]{5}" placeholder="např. 11000">
                </div>
                <div class="form-group">
                    <label for="excluded-countries">Nechci zemi původu (volitelné):</label>
                    <input type="text" id="excluded-countries" placeholder="např. PL, RO">
                    <small>Zadejte zkratky zemí oddělené čárkou</small>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="no-rotated" name="no-rotated">
                        Nechci otočenou orientaci (zakáže otáčení desek o 90°)
                    </label>
                    <small>Pokud je zaškrtnuto, desky nebudou otáčeny ani když by to umožnilo výrobu</small>
                </div>

                <div class="form-group">
                    <label for="order-file">Vyberte CSV soubor s objednávkou:</label>
                    <input type="file" id="order-file" accept=".csv" required>
                </div>
                <button type="submit">Zpracovat objednávku</button>
            </form>
            <div id="order-result" class="result-box hidden"></div>
        </div>

                <div class="card hidden" id="results-card">
                    <h2>Výsledky zpracování objednávky</h2>
                    <div id="results-content"></div>
                </div>
            </div>

            <!-- Nápověda - 1/3 stránky -->
            <div class="help-area">
                <div class="card">
                    <div class="help-section">
                        <h3>📋 Návod k použití</h3>

                        <div class="help-step">
                            <h4>1️⃣ Nahrajte CSV soubor skladu</h4>
                            <p>Soubor s materiály ve skladech CZ21, CZ23 a SK11</p>
                        </div>

                        <div class="help-step">
                            <h4>2️⃣ Zadejte PSČ zákazníka</h4>
                            <p>Pro výpočet vzdáleností a optimalizaci</p>
                        </div>

                        <div class="help-step">
                            <h4>3️⃣ Nahrajte objednávku</h4>
                            <p>CSV s položkami, rozměry a množstvím</p>
                        </div>

                        <div class="help-step">
                            <h4>4️⃣ Zpracování objednávky</h4>
                            <p>Automatické zpracování a výsledky</p>
                        </div>

                        <div class="help-symbols">
                            <h4>🔍 Význam symbolů</h4>
                            <div class="symbol-item">
                                <span class="symbol available">✅</span>
                                <span>Materiál dostupný</span>
                            </div>
                            <div class="symbol-item">
                                <span class="symbol reserved">R</span>
                                <span>Pouze rezervované karty</span>
                            </div>
                            <div class="symbol-item">
                                <span class="symbol unavailable">❌</span>
                                <span>Nedostupný</span>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SheetJS knihovna pro zpracování Excel souborů -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Globální proměnné
            let warehouseData = null;
            let orderData = null;
            let excludedCountries = [];
            let distancesData = [];
            let processedWarehouseData = {
                // Struktura pro rychlé vyhledávání položek ve skladu
                // Klíč: základní název materiálu (např. "ENAW5754H111 25,00")
                // Hodnota: pole položek s tímto základním názvem
                materialsByBaseName: {},

                // Struktura pro rychlé vyhledávání položek podle přesného názvu
                // Klíč: přesný název položky (např. "ENAW5754H111 25,00x1520,0x3020,0")
                // Hodnota: pole položek s tímto přesným názvem
                materialsByExactName: {}
            };

            // Elementy formulářů
            const warehouseForm = document.getElementById('warehouse-form');
            const warehouseFileInput = document.getElementById('warehouse-file');
            const warehouseResult = document.getElementById('warehouse-result');
            const warehouseCard = document.getElementById('warehouse-card');

            const orderForm = document.getElementById('order-form');
            const orderFileInput = document.getElementById('order-file');
            const postalCodeInput = document.getElementById('postal-code');
            const excludedCountriesInput = document.getElementById('excluded-countries');
            const orderResult = document.getElementById('order-result');
            const orderCard = document.getElementById('order-card');

            const resultsCard = document.getElementById('results-card');
            const resultsContent = document.getElementById('results-content');

            // Načtení dat vzdáleností při startu aplikace
            loadDistancesData();

            // Zpracování formuláře skladu
            warehouseForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Kontrola, zda byl vybrán soubor
                if (warehouseFileInput.files.length === 0) {
                    showResult(warehouseResult, 'Vyberte prosím soubor se stavem skladu.', 'error');
                    return;
                }

                const file = warehouseFileInput.files[0];
                console.log('Vybraný soubor skladu:', file.name);

                // Zobrazení informace o zpracování
                showResult(warehouseResult, 'Zpracovávám soubor skladu...', 'info');

                // Čtení souboru
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // Zpracování CSV souboru pomocí SheetJS
                        const csvContent = e.target.result;
                        const workbook = XLSX.read(csvContent, {
                            type: 'string',
                            raw: true,
                            cellDates: true,
                            dateNF: 'yyyy-mm-dd',
                            FS: ';' // Středník jako oddělovač pro české CSV
                        });

                        // Získání prvního listu
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        // Konverze na JSON
                        warehouseData = XLSX.utils.sheet_to_json(worksheet);

                        if (warehouseData.length === 0) {
                            // Zkusíme jiný oddělovač
                            const workbook2 = XLSX.read(csvContent, {
                                type: 'string',
                                raw: true,
                                cellDates: true,
                                dateNF: 'yyyy-mm-dd',
                                FS: ',' // Čárka jako oddělovač pro anglické CSV
                            });

                            const firstSheetName2 = workbook2.SheetNames[0];
                            const worksheet2 = workbook2.Sheets[firstSheetName2];
                            warehouseData = XLSX.utils.sheet_to_json(worksheet2);
                        }

                        if (warehouseData.length === 0) {
                            showResult(warehouseResult, 'Soubor neobsahuje žádná data nebo má nesprávný formát.', 'error');
                            return;
                        }

                        console.log('Načtená data skladu:', warehouseData);

                        // Zpracování dat skladu pro efektivní vyhledávání
                        processWarehouseData(warehouseData);

                        // Zobrazení informace o úspěšném nahrání
                        showResult(warehouseResult, `Stav skladu byl úspěšně nahrán. Načteno ${warehouseData.length} položek.`, 'success');

                        // Zobrazení formuláře pro objednávku
                        orderCard.classList.remove('hidden');
                    } catch (error) {
                        console.error('Chyba při zpracování souboru skladu:', error);
                        showResult(warehouseResult, `Chyba při zpracování souboru: ${error.message}`, 'error');
                    }
                };

                reader.onerror = function() {
                    showResult(warehouseResult, 'Chyba při čtení souboru.', 'error');
                };

                reader.readAsText(file);
            });

            // Zpracování formuláře objednávky
            orderForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Kontrola, zda byl vybrán soubor
                if (orderFileInput.files.length === 0) {
                    showResult(orderResult, 'Vyberte prosím soubor s objednávkou.', 'error');
                    return;
                }

                // Kontrola, zda bylo zadáno PSČ
                const postalCode = postalCodeInput.value.trim();
                if (!postalCode || !/^\d{5}$/.test(postalCode)) {
                    showResult(orderResult, 'Zadejte prosím platné PSČ (5 číslic).', 'error');
                    return;
                }

                // Zpracování vyloučených zemí
                excludedCountries = [];
                const excludedCountriesText = excludedCountriesInput.value.trim();
                if (excludedCountriesText) {
                    // Rozdělení textu podle čárek a odstranění mezer
                    excludedCountries = excludedCountriesText.split(',').map(country => country.trim().toUpperCase());
                    console.log('Vyloučené země:', excludedCountries);
                }

                // Zpracování nastavení otáčení
                const noRotatedCheckbox = document.getElementById('no-rotated');
                const noRotatedGlobal = noRotatedCheckbox && noRotatedCheckbox.checked;
                console.log('Zakázaná otočená orientace:', noRotatedGlobal);
                console.log('Checkbox element:', noRotatedCheckbox);
                console.log('Checkbox checked:', noRotatedCheckbox ? noRotatedCheckbox.checked : 'N/A');

                const file = orderFileInput.files[0];
                console.log('Vybraný soubor objednávky:', file.name);

                // Zobrazení informace o zpracování
                showResult(orderResult, 'Zpracovávám soubor objednávky...', 'info');

                // Čtení souboru
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        // Zpracování CSV souboru pomocí SheetJS
                        const csvContent = e.target.result;
                        const workbook = XLSX.read(csvContent, {
                            type: 'string',
                            raw: true,
                            cellDates: true,
                            dateNF: 'yyyy-mm-dd',
                            FS: ';' // Středník jako oddělovač pro české CSV
                        });

                        // Získání prvního listu
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];

                        // Konverze na JSON
                        orderData = XLSX.utils.sheet_to_json(worksheet);

                        if (orderData.length === 0) {
                            // Zkusíme jiný oddělovač
                            const workbook2 = XLSX.read(csvContent, {
                                type: 'string',
                                raw: true,
                                cellDates: true,
                                dateNF: 'yyyy-mm-dd',
                                FS: ',' // Čárka jako oddělovač pro anglické CSV
                            });

                            const firstSheetName2 = workbook2.SheetNames[0];
                            const worksheet2 = workbook2.Sheets[firstSheetName2];
                            orderData = XLSX.utils.sheet_to_json(worksheet2);
                        }

                        if (orderData.length === 0) {
                            showResult(orderResult, 'Soubor neobsahuje žádná data nebo má nesprávný formát.', 'error');
                            return;
                        }

                        console.log('Načtená data objednávky:', orderData);

                        // Zobrazení informace o úspěšném nahrání
                        showResult(orderResult, `Objednávka byla úspěšně nahrána. Načteno ${orderData.length} položek.`, 'success');

                        // Zpracování objednávky a skladu
                        processOrderAndWarehouse(orderData, warehouseData, postalCode, noRotatedGlobal);
                    } catch (error) {
                        console.error('Chyba při zpracování souboru objednávky:', error);
                        showResult(orderResult, `Chyba při zpracování souboru: ${error.message}`, 'error');
                    }
                };

                reader.onerror = function() {
                    showResult(orderResult, 'Chyba při čtení souboru.', 'error');
                };

                reader.readAsText(file);
            });

            // Funkce pro zpracování objednávky a skladu - obecné řešení
            async function processOrderAndWarehouse(orderData, warehouseData, postalCode, noRotatedGlobal = false) {
                console.log('🔄 processOrderAndWarehouse zavoláno s noRotatedGlobal:', noRotatedGlobal);
                
                // Zobrazení karty s výsledky
                resultsCard.classList.remove('hidden');

                // Výpočet vzdáleností od zákazníka
                const distances = await calculateDistances(postalCode);

                // Vytvoření výsledků
                const results = {
                    availability: {},
                    optimal_location: {
                        location: 'TBD', // Bude určeno později
                        reason: 'TBD', // Bude určeno později
                        distances: distances
                    }
                };

                // Vytvoření struktury pro sledování celkového požadovaného množství pro každý materiál
                const materialTotals = {};

                // Výpis dostupných sloupců v objednávce pro diagnostiku
                if (orderData.length > 0) {
                    console.log('Dostupné sloupce v objednávce:', Object.keys(orderData[0]));
                }

                // Krok 1: Shromáždění všech položek objednávky a jejich seskupení podle základního názvu materiálu
                const orderItems = [];

                // Zpracování objednávky - nejprve shromáždíme všechny položky
                for (const orderItem of orderData) {
                    // Získání popisu položky z různých možných sloupců
                    let productName = null;

                    // Seznam možných názvů sloupců pro popis položky
                    const possibleProductColumns = [
                        'product', 'Product', 'PRODUCT',
                        'Popis položky', 'popis položky', 'POPIS POLOŽKY',
                        'Popis polozky', 'popis polozky', 'POPIS POLOZKY',
                        'Č. položky zákazníka', 'č. položky zákazníka',
                        'Cislo polozky', 'cislo polozky', 'CISLO POLOZKY',
                        'Číslo položky', 'číslo položky', 'ČÍSLO POLOŽKY',
                        'Název', 'název', 'NÁZEV',
                        'Name', 'name', 'NAME',
                        'Materiál', 'materiál', 'MATERIÁL',
                        'Material', 'material', 'MATERIAL',
                        'Popis', 'popis', 'POPIS',
                        'Description', 'description', 'DESCRIPTION'
                    ];

                    // Procházení možných názvů sloupců
                    for (const column of possibleProductColumns) {
                        if (orderItem[column] !== undefined && orderItem[column] !== null && orderItem[column] !== '') {
                            productName = orderItem[column];
                            break;
                        }
                    }

                    // Pokud nebyl nalezen název položky, použijeme první neprázdnou hodnotu
                    if (!productName) {
                        for (const [key, value] of Object.entries(orderItem)) {
                            if (value !== undefined && value !== null && value !== '') {
                                productName = value;
                                break;
                            }
                        }
                    }

                    // Pokud stále nemáme název, použijeme výchozí
                    if (!productName) {
                        productName = 'Položka ' + (orderItems.length + 1);
                    }

                    // Získání množství z různých možných sloupců
                    let quantity = null;

                    // Seznam možných názvů sloupců pro množství
                    const possibleQuantityColumns = [
                        'quantity', 'Quantity', 'QUANTITY',
                        'Množství', 'množství', 'MNOŽSTVÍ',
                        'Mnozstvi', 'mnozstvi', 'MNOZSTVI',
                        'Počet', 'počet', 'POČET',
                        'Pocet', 'pocet', 'POCET',
                        'Ks', 'ks', 'KS',
                        'Kusy', 'kusy', 'KUSY',
                        'Požadované množství', 'požadované množství', 'POŽADOVANÉ MNOŽSTVÍ',
                        'Pozadovane mnozstvi', 'pozadovane mnozstvi', 'POZADOVANE MNOZSTVI',
                        'Požadované mn.', 'požadované mn.', 'POŽADOVANÉ MN.'
                    ];

                    // Procházení možných názvů sloupců
                    for (const column of possibleQuantityColumns) {
                        if (orderItem[column] !== undefined && orderItem[column] !== null && orderItem[column] !== '') {
                            quantity = parseFloat(orderItem[column].toString().replace(',', '.'));
                            break;
                        }
                    }

                    // Pokud nebyla nalezena hodnota množství, zobrazíme vyskakovací okno
                    if (!quantity) {
                        // Zobrazení vyskakovacího okna pro zadání množství
                        quantity = promptForValue(`Zadejte požadované množství pro položku "${productName}":`);

                        // Kontrola, zda bylo zadáno platné číslo
                        if (!quantity || isNaN(quantity)) {
                            showResult(orderResult, `Chyba: Nebylo zadáno platné množství pro položku "${productName}".`, 'error');
                            throw new Error(`Nebylo zadáno platné množství pro položku "${productName}".`);
                        }
                    }

                    // Získání rozměrů z různých možných sloupců
                    let width = null;
                    let length = null;

                    // Seznam možných názvů sloupců pro šířku
                    const possibleWidthColumns = [
                        'width', 'Width', 'WIDTH',
                        'Šířka', 'šířka', 'ŠÍŘKA',
                        'Sirka', 'sirka', 'SIRKA',
                        'Šířka (mm)', 'šířka (mm)', 'ŠÍŘKA (MM)',
                        'Sirka (mm)', 'sirka (mm)', 'SIRKA (MM)'
                    ];

                    // Seznam možných názvů sloupců pro délku
                    const possibleLengthColumns = [
                        'length', 'Length', 'LENGTH',
                        'Délka', 'délka', 'DÉLKA',
                        'Delka', 'delka', 'DELKA',
                        'Délka (mm)', 'délka (mm)', 'DÉLKA (MM)',
                        'Delka (mm)', 'delka (mm)', 'DELKA (MM)'
                    ];

                    // Procházení možných názvů sloupců pro šířku
                    for (const column of possibleWidthColumns) {
                        if (orderItem[column] !== undefined && orderItem[column] !== null && orderItem[column] !== '') {
                            width = parseFloat(orderItem[column].toString().replace(',', '.'));
                            break;
                        }
                    }

                    // Procházení možných názvů sloupců pro délku
                    for (const column of possibleLengthColumns) {
                        if (orderItem[column] !== undefined && orderItem[column] !== null && orderItem[column] !== '') {
                            length = parseFloat(orderItem[column].toString().replace(',', '.'));
                            break;
                        }
                    }

                    // Pokud nebyly nalezeny rozměry, zobrazíme vyskakovací okno
                    if (!width) {
                        // Zobrazení vyskakovacího okna pro zadání šířky
                        width = promptForValue(`Zadejte šířku (mm) pro položku "${productName}":`);

                        // Kontrola, zda byla zadána platná hodnota
                        if (!width || isNaN(width)) {
                            showResult(orderResult, `Chyba: Nebyla zadána platná šířka pro položku "${productName}".`, 'error');
                            throw new Error(`Nebyla zadána platná šířka pro položku "${productName}".`);
                        }
                    }

                    if (!length) {
                        // Zobrazení vyskakovacího okna pro zadání délky
                        length = promptForValue(`Zadejte délku (mm) pro položku "${productName}":`);

                        // Kontrola, zda byla zadána platná hodnota
                        if (!length || isNaN(length)) {
                            showResult(orderResult, `Chyba: Nebyla zadána platná délka pro položku "${productName}".`, 'error');
                            throw new Error(`Nebyla zadána platná délka pro položku "${productName}".`);
                        }
                    }

                    // Extrahujeme základní název materiálu (např. "ENAW5754H111 20,00" z "ENAW5754H111 20,00x40,0x55,0")
                    let baseMaterial = extractBaseMaterialName(productName);

                    console.log(`Extrahovaný základní název materiálu: "${baseMaterial}" z "${productName}"`);

                    // Přidání položky do pole
                    orderItems.push({
                        productName: productName,
                        baseMaterial: baseMaterial,
                        quantity: quantity,
                        width: width,
                        length: length
                    });

                    // Aktualizace celkového množství pro tento materiál
                    if (!materialTotals[baseMaterial]) {
                        materialTotals[baseMaterial] = {
                            quantity: 0,
                            maxWidth: 0,
                            maxLength: 0,
                            items: [] // Pole pro sledování jednotlivých položek
                        };
                    }

                    // Přidání 6mm prořezu na všech stranách
                    const SAW_WIDTH = 6; // mm
                    const adjustedWidth = width + (2 * SAW_WIDTH);  // +6mm na každou stranu
                    const adjustedLength = length + (2 * SAW_WIDTH); // +6mm na každou stranu

                    console.log(`  Původní rozměry: ${width}x${length} mm`);
                    console.log(`  S prořezem (+${SAW_WIDTH}mm na každou stranu): ${adjustedWidth}x${adjustedLength} mm`);

                    // Přidání položky do seznamu
                    materialTotals[baseMaterial].items.push({
                        productName: productName,
                        quantity: quantity,
                        width: adjustedWidth,
                        length: adjustedLength,
                        originalWidth: width,
                        originalLength: length,
                        area: parseFloat(((adjustedWidth * adjustedLength * quantity) / 1000000).toFixed(6))
                    });

                    // Podrobné logování
                    console.log(`Přidávám položku do materiálu "${baseMaterial}":`);
                    console.log(`  Název: ${productName}`);
                    console.log(`  Množství: ${quantity}`);
                    console.log(`  Původní rozměry: ${width}x${length} mm`);
                    console.log(`  Rozměry s prořezem: ${adjustedWidth}x${adjustedLength} mm`);
                    console.log(`  Plocha s prořezem: ${parseFloat(((adjustedWidth * adjustedLength * quantity) / 1000000).toFixed(6))} m²`);

                    materialTotals[baseMaterial].quantity += quantity;
                    materialTotals[baseMaterial].maxWidth = Math.max(materialTotals[baseMaterial].maxWidth, adjustedWidth);
                    materialTotals[baseMaterial].maxLength = Math.max(materialTotals[baseMaterial].maxLength, adjustedLength);
                }

                console.log('Shromážděné položky objednávky:', orderItems);
                console.log('Celkové množství podle materiálů:', materialTotals);

                // Krok 2: Zpracování položek a vytvoření výsledků
                for (const [baseMaterial, totals] of Object.entries(materialTotals)) {
                    // Výpočet celkové plochy pro tento materiál
                    let totalArea = 0;

                    // Podrobné logování jednotlivých položek
                    console.log(`\nZpracování materiálu "${baseMaterial}":`);
                    console.log(`Počet položek: ${totals.items.length}`);

                    for (const item of totals.items) {
                        console.log(`  Položka: ${item.productName}`);
                        console.log(`    Množství: ${item.quantity}`);
                        console.log(`    Rozměry: ${item.width}x${item.length} mm`);
                        console.log(`    Plocha: ${item.area} m²`);
                        totalArea += item.area;
                    }

                    console.log(`  Celková plocha: ${totalArea} m²`);

                    // Hledání odpovídajících položek ve skladu
                    const matchingItems = findMatchingItems({
                        productName: baseMaterial,
                        quantity: totals.quantity,
                        width: totals.maxWidth,
                        length: totals.maxLength
                    }, warehouseData, noRotatedGlobal);

                    // Kontrola, zda máme nějaké položky ve skladu (včetně rezervovaných)
                    const hasItemsInWarehouse = (matchingItems.CZ21.items && matchingItems.CZ21.items.length > 0) ||
                                              (matchingItems.CZ23.items && matchingItems.CZ23.items.length > 0) ||
                                              (matchingItems.SK11.items && matchingItems.SK11.items.length > 0);

                    // Kontrola, zda máme rezervované položky
                    const hasReservedItems = (matchingItems.CZ21.items && matchingItems.CZ21.items.some(item => item.reservedQuantity > 0)) ||
                                           (matchingItems.CZ23.items && matchingItems.CZ23.items.some(item => item.reservedQuantity > 0)) ||
                                           (matchingItems.SK11.items && matchingItems.SK11.items.some(item => item.reservedQuantity > 0));

                    // Pokud nemáme žádné položky ve skladu, materiál není dostupný
                    if (!hasItemsInWarehouse) {
                        console.log(`Nenalezeny žádné položky pro ${baseMaterial} ve skladu - materiál není dostupný.`);
                        console.log(`  CZ21: 0 m² (nedostupný)`);
                        console.log(`  CZ23: 0 m² (nedostupný)`);
                        console.log(`  SK11: 0 m² (nedostupný)`);

                        // Nastavíme plochy na 0 - materiál není dostupný
                        matchingItems.CZ21.area = 0;
                        matchingItems.CZ23.area = 0;
                        matchingItems.SK11.area = 0;
                    } else if (matchingItems.CZ21.area === 0 && matchingItems.CZ23.area === 0 && matchingItems.SK11.area === 0) {
                        // Máme položky ve skladu, ale všechny jsou rezervované nebo nedostupné
                        if (hasReservedItems) {
                            console.log(`Materiál ${baseMaterial} je ve skladu jako rezervované karty.`);
                            console.log(`  CZ21: ${matchingItems.CZ21.area} m² (dostupné), ${matchingItems.CZ21.items ? matchingItems.CZ21.items.filter(item => item.reservedQuantity > 0).length : 0} rezervovaných karet`);
                            console.log(`  CZ23: ${matchingItems.CZ23.area} m² (dostupné), ${matchingItems.CZ23.items ? matchingItems.CZ23.items.filter(item => item.reservedQuantity > 0).length : 0} rezervovaných karet`);
                            console.log(`  SK11: ${matchingItems.SK11.area} m² (dostupné), ${matchingItems.SK11.items ? matchingItems.SK11.items.filter(item => item.reservedQuantity > 0).length : 0} rezervovaných karet`);
                        } else {
                            console.log(`Materiál ${baseMaterial} je ve skladu, ale není dostupný (nedostupný).`);
                            console.log(`  CZ21: ${matchingItems.CZ21.area} m² (dostupné)`);
                            console.log(`  CZ23: ${matchingItems.CZ23.area} m² (dostupné)`);
                            console.log(`  SK11: ${matchingItems.SK11.area} m² (dostupné)`);
                        }
                    }

                    // Vytvoření položky ve výsledcích
                    results.availability[baseMaterial] = {
                        locations: {
                            'A': matchingItems.CZ21 ? {
                                quantity: matchingItems.CZ21.quantity || 0,
                                width: matchingItems.CZ21.width || 0,
                                length: matchingItems.CZ21.length || 0,
                                area: matchingItems.CZ21.area || 0,
                                items: matchingItems.CZ21.items || []
                            } : null,
                            'B': matchingItems.CZ23 ? {
                                quantity: matchingItems.CZ23.quantity || 0,
                                width: matchingItems.CZ23.width || 0,
                                length: matchingItems.CZ23.length || 0,
                                area: matchingItems.CZ23.area || 0,
                                items: matchingItems.CZ23.items || []
                            } : null,
                            'D': matchingItems.SK11 ? {
                                quantity: matchingItems.SK11.quantity || 0,
                                width: matchingItems.SK11.width || 0,
                                length: matchingItems.SK11.length || 0,
                                area: matchingItems.SK11.area || 0,
                                items: matchingItems.SK11.items || []
                            } : null
                        },
                        required_quantity: totals.quantity,
                        dimensions_required: true,
                        required_width: totals.maxWidth,
                        required_length: totals.maxLength,
                        // Použití vypočítané celkové plochy místo výpočtu z maxWidth a maxLength
                        required_area: totalArea
                    };

                    console.log(`Výsledky pro ${baseMaterial}:`);
                    console.log(`  CZ21 plocha: ${matchingItems.CZ21.area} m²`);
                    console.log(`  CZ23 plocha: ${matchingItems.CZ23.area} m²`);
                    console.log(`  SK11 plocha: ${matchingItems.SK11.area} m²`);
                    console.log(`  Požadovaná plocha: ${totalArea} m²`);
                }

                // Určení optimálního místa výroby
                const optimalLocation = determineOptimalLocation(results, distances);
                results.optimal_location = optimalLocation;

                // Zobrazení výsledků
                displayResults(results);
            }

            // Funkce pro určení optimálního místa výroby
            function determineOptimalLocation(results, distances) {
                console.log('Určuji optimální místo výroby...');

                // Analýza dostupnosti materiálů
                let cz21AvailableCount = 0;
                let cz23AvailableCount = 0;
                let totalMaterials = 0;
                let cz21TotalArea = 0;
                let cz23TotalArea = 0;
                let totalRequiredArea = 0;

                for (const [material, info] of Object.entries(results.availability)) {
                    totalMaterials++;

                    const cz21Area = info.locations?.A?.area || 0;
                    const cz23Area = info.locations?.B?.area || 0;
                    const requiredArea = info.required_area || 0;

                    cz21TotalArea += cz21Area;
                    cz23TotalArea += cz23Area;
                    totalRequiredArea += requiredArea;

                    // Kontrola dostupnosti pomocí nové logiky
                    const cz21Status = determineAvailabilityStatus(material, 'CZ21', cz21Area, requiredArea, results);
                    const cz23Status = determineAvailabilityStatus(material, 'CZ23', cz23Area, requiredArea, results);

                    const cz21Available = cz21Status.available;
                    const cz23Available = cz23Status.available;

                    if (cz21Available) cz21AvailableCount++;
                    if (cz23Available) cz23AvailableCount++;

                    console.log(`Materiál ${material}:`);
                    console.log(`  CZ21: ${cz21Area.toFixed(6)} m² (${cz21Available ? 'dostupný' : 'nedostupný'}) - ${cz21Status.symbol}`);
                    console.log(`  CZ23: ${cz23Area.toFixed(6)} m² (${cz23Available ? 'dostupný' : 'nedostupný'}) - ${cz23Status.symbol}`);
                    console.log(`  Požadováno: ${requiredArea.toFixed(6)} m²`);
                }

                console.log(`Celková analýza:`);
                console.log(`  CZ21: ${cz21AvailableCount}/${totalMaterials} materiálů dostupných`);
                console.log(`  CZ23: ${cz23AvailableCount}/${totalMaterials} materiálů dostupných`);
                console.log(`  Vzdálenosti: CZ21=${distances.CZ21}km, CZ23=${distances.CZ23}km`);

                // Logika rozhodování
                let location, reason;

                // 1. Pokud má jedno místo všechny materiály a druhé ne
                if (cz21AvailableCount === totalMaterials && cz23AvailableCount < totalMaterials) {
                    location = 'CZ21';
                    reason = `Všechny materiály (${cz21AvailableCount}/${totalMaterials}) jsou dostupné pouze v CZ21 (Bruntál)`;
                } else if (cz23AvailableCount === totalMaterials && cz21AvailableCount < totalMaterials) {
                    location = 'CZ23';
                    reason = `Všechny materiály (${cz23AvailableCount}/${totalMaterials}) jsou dostupné pouze v CZ23 (České Budějovice)`;
                }
                // 2. Pokud mají obě místa všechny materiály, vybrat nejbližší
                else if (cz21AvailableCount === totalMaterials && cz23AvailableCount === totalMaterials) {
                    if (distances.CZ21 <= distances.CZ23) {
                        location = 'CZ21';
                        reason = `Všechny materiály dostupné na obou místech, vybráno nejbližší místo CZ21 (${distances.CZ21}km vs ${distances.CZ23}km)`;
                    } else {
                        location = 'CZ23';
                        reason = `Všechny materiály dostupné na obou místech, vybráno nejbližší místo CZ23 (${distances.CZ23}km vs ${distances.CZ21}km)`;
                    }
                }
                // 3. Pokud má jedno místo více materiálů než druhé
                else if (cz21AvailableCount > cz23AvailableCount) {
                    location = 'CZ21';
                    reason = `Více materiálů dostupných v CZ21 (${cz21AvailableCount}/${totalMaterials} vs ${cz23AvailableCount}/${totalMaterials})`;
                } else if (cz23AvailableCount > cz21AvailableCount) {
                    location = 'CZ23';
                    reason = `Více materiálů dostupných v CZ23 (${cz23AvailableCount}/${totalMaterials} vs ${cz21AvailableCount}/${totalMaterials})`;
                }
                // 4. Pokud mají stejný počet materiálů, vybrat podle celkové plochy
                else if (cz21AvailableCount === cz23AvailableCount) {
                    if (cz21TotalArea >= cz23TotalArea) {
                        location = 'CZ21';
                        reason = `Stejný počet materiálů (${cz21AvailableCount}/${totalMaterials}), ale větší celková plocha v CZ21 (${cz21TotalArea.toFixed(2)} vs ${cz23TotalArea.toFixed(2)} m²)`;
                    } else {
                        location = 'CZ23';
                        reason = `Stejný počet materiálů (${cz23AvailableCount}/${totalMaterials}), ale větší celková plocha v CZ23 (${cz23TotalArea.toFixed(2)} vs ${cz21TotalArea.toFixed(2)} m²)`;
                    }
                }
                // 5. Fallback - vybrat nejbližší
                else {
                    if (distances.CZ21 <= distances.CZ23) {
                        location = 'CZ21';
                        reason = `Vybráno nejbližší místo CZ21 (${distances.CZ21}km)`;
                    } else {
                        location = 'CZ23';
                        reason = `Vybráno nejbližší místo CZ23 (${distances.CZ23}km)`;
                    }
                }

                console.log(`Rozhodnutí: ${location} - ${reason}`);

                return {
                    location: location,
                    reason: reason,
                    distances: distances,
                    analysis: {
                        cz21AvailableCount: cz21AvailableCount,
                        cz23AvailableCount: cz23AvailableCount,
                        totalMaterials: totalMaterials,
                        cz21TotalArea: cz21TotalArea,
                        cz23TotalArea: cz23TotalArea,
                        totalRequiredArea: totalRequiredArea
                    }
                };
            }

            // Funkce pro hledání odpovídajících položek ve skladu - používá předem vypočítané plochy
            function findMatchingItems(orderItem, warehouseData, noRotatedGlobal = false) {
                console.log('🔍 findMatchingItems zavoláno s noRotatedGlobal:', noRotatedGlobal);
                
                // Získání informací o položce
                const productName = orderItem.productName || '';
                const baseMaterial = orderItem.baseMaterial || extractBaseMaterialName(productName);
                const quantity = orderItem.quantity || 0;
                const width = orderItem.width || 0;
                const length = orderItem.length || 0;

                console.log('Hledám položku:', productName);
                console.log('Základní materiál:', baseMaterial);
                console.log('Množství:', quantity);
                console.log('Rozměry:', width, 'x', length, 'mm');

                // Hledání odpovídajících položek ve skladu podle základního názvu materiálu
                console.log(`Hledám položky pro materiál "${baseMaterial}" ve skladu`);

                // Inicializace výsledků
                let cz21Items = [];
                let cz23Items = [];
                let sk11Items = [];

                // Hledání položek ve zpracovaných datech skladu
                if (processedWarehouseData.materialsByBaseName[baseMaterial]) {
                    // Procházení všech položek s tímto základním názvem
                    for (const item of processedWarehouseData.materialsByBaseName[baseMaterial]) {
                        // Kontrola země původu - přeskočíme položky z vyloučených zemí
                        if (excludedCountries.length > 0 && item.country && excludedCountries.includes(item.country.toUpperCase())) {
                            console.log(`Přeskakuji položku z vyloučené země: ${item.country}`);
                            continue;
                        }

                        // Rozdělení položek podle lokace
                        if (item.location && item.location.includes('CZ21')) {
                            cz21Items.push(item);
                        } else if (item.location && item.location.includes('CZ23')) {
                            cz23Items.push(item);
                        } else if (item.location && item.location.includes('SK11')) {
                            sk11Items.push(item);
                        }
                    }
                }

                console.log(`Nalezeno ${cz21Items.length} položek v CZ21, ${cz23Items.length} položek v CZ23 a ${sk11Items.length} položek v SK11`);

                // Výpočet celkové plochy pro CZ21
                let cz21TotalArea = 0;
                let cz21Width = 0;
                let cz21Length = 0;

                console.log(`\nVýpočet plochy pro CZ21 (${cz21Items.length} položek):`);

                for (const item of cz21Items) {
                    // Použití plochy z celkového množství na skladě (ne jen dostupného)
                    const itemTotalArea = (item.width * item.length * item.quantity) / 1000000;
                    cz21TotalArea += itemTotalArea;

                    console.log(`  Položka: ${item.description}`);
                    console.log(`    Celkové množství: ${item.quantity}`);
                    console.log(`    Rezervované množství: ${item.reservedQuantity || 0}`);
                    console.log(`    Dostupné množství: ${item.availableQuantity}`);
                    console.log(`    Rozměry: ${item.width}x${item.length} mm`);
                    console.log(`    Plocha z celkového množství: ${itemTotalArea.toFixed(6)} m²`);

                    // Použití rozměrů první položky jako výchozích
                    if (cz21Width === 0) {
                        cz21Width = item.width;
                        cz21Length = item.length;
                    }
                }

                console.log(`  Celková plocha CZ21: ${cz21TotalArea} m²`);

                // Výpočet celkové plochy pro CZ23
                let cz23TotalArea = 0;
                let cz23Width = 0;
                let cz23Length = 0;

                console.log(`\nVýpočet plochy pro CZ23 (${cz23Items.length} položek):`);

                for (const item of cz23Items) {
                    // Použití plochy z celkového množství na skladě (ne jen dostupného)
                    const itemTotalArea = (item.width * item.length * item.quantity) / 1000000;
                    cz23TotalArea += itemTotalArea;

                    console.log(`  Položka: ${item.description}`);
                    console.log(`    Celkové množství: ${item.quantity}`);
                    console.log(`    Rezervované množství: ${item.reservedQuantity || 0}`);
                    console.log(`    Dostupné množství: ${item.availableQuantity}`);
                    console.log(`    Rozměry: ${item.width}x${item.length} mm`);
                    console.log(`    Plocha z celkového množství: ${itemTotalArea.toFixed(6)} m²`);

                    // Použití rozměrů první položky jako výchozích
                    if (cz23Width === 0) {
                        cz23Width = item.width;
                        cz23Length = item.length;
                    }
                }

                console.log(`  Celková plocha CZ23: ${cz23TotalArea} m²`);

                // Výpočet celkové plochy pro SK11
                let sk11TotalArea = 0;
                let sk11Width = 0;
                let sk11Length = 0;

                console.log(`\nVýpočet plochy pro SK11 (${sk11Items.length} položek):`);

                for (const item of sk11Items) {
                    // Použití plochy z celkového množství na skladě (ne jen dostupného)
                    const itemTotalArea = (item.width * item.length * item.quantity) / 1000000;
                    sk11TotalArea += itemTotalArea;

                    console.log(`  Položka: ${item.description}`);
                    console.log(`    Celkové množství: ${item.quantity}`);
                    console.log(`    Rezervované množství: ${item.reservedQuantity || 0}`);
                    console.log(`    Dostupné množství: ${item.availableQuantity}`);
                    console.log(`    Rozměry: ${item.width}x${item.length} mm`);
                    console.log(`    Plocha z celkového množství: ${itemTotalArea.toFixed(6)} m²`);

                    // Použití rozměrů první položky jako výchozích
                    if (sk11Width === 0) {
                        sk11Width = item.width;
                        sk11Length = item.length;
                    }
                }

                console.log(`  Celková plocha SK11: ${sk11TotalArea} m²`);

                // Pokud nemáme žádné položky, použijeme výchozí hodnoty
                if (cz21Width === 0) {
                    cz21Width = width || 1000;
                    cz21Length = length || 2000;
                }

                if (cz23Width === 0) {
                    cz23Width = width || 1000;
                    cz23Length = length || 2000;
                }

                if (sk11Width === 0) {
                    sk11Width = width || 1000;
                    sk11Length = length || 2000;
                }

                // Zaokrouhlení ploch na 3 desetinná místa
                cz21TotalArea = parseFloat(cz21TotalArea.toFixed(3));
                cz23TotalArea = parseFloat(cz23TotalArea.toFixed(3));
                sk11TotalArea = parseFloat(sk11TotalArea.toFixed(3));

                console.log(`CZ21: ${cz21TotalArea} m²`);
                console.log(`CZ23: ${cz23TotalArea} m²`);
                console.log(`SK11: ${sk11TotalArea} m²`);

                return {
                    CZ21: {
                        quantity: cz21Items.length,
                        width: cz21Width,
                        length: cz21Length,
                        area: cz21TotalArea,
                        items: cz21Items
                    },
                    CZ23: {
                        quantity: cz23Items.length,
                        width: cz23Width,
                        length: cz23Length,
                        area: cz23TotalArea,
                        items: cz23Items
                    },
                    SK11: {
                        quantity: sk11Items.length,
                        width: sk11Width,
                        length: sk11Length,
                        area: sk11TotalArea,
                        items: sk11Items
                    }
                };
            }

            // Funkce pro zobrazení výsledků
            function displayResults(results) {
                // Vytvoření HTML pro výsledky
                let html = `
                    <h3>Dostupnost produktů podle umístění</h3>
                    <table>
                        <tr>
                            <th>Materiál</th>
                            <th>Množství na skladě CZ21</th>
                            <th>Množství na skladě CZ23</th>
                            <th>Množství na skladě SK11</th>
                            <th>Požadované množství</th>
                            <th>CZ21</th>
                            <th>CZ23</th>
                            <th>SK11</th>
                        </tr>
                `;

                // Seskupení materiálů podle základního názvu
                const materialGroups = {};

                // Procházení všech produktů a seskupování podle základního názvu
                for (const [product, info] of Object.entries(results.availability)) {
                    // Extrahujeme základní název materiálu (např. "ENAW5754H111 20,00" z "ENAW5754H111 20,00x40,0x55,0")
                    let baseMaterial = extractBaseMaterialName(product);

                    // Pokud skupina ještě neexistuje, vytvoříme ji
                    if (!materialGroups[baseMaterial]) {
                        materialGroups[baseMaterial] = {
                            cz21Area: 0,
                            cz23Area: 0,
                            sk11Area: 0,
                            requiredArea: 0,
                            products: []
                        };
                    }

                    // Přidáme produkt do skupiny
                    materialGroups[baseMaterial].products.push({
                        product: product,
                        info: info
                    });
                }

                // Seřadíme materiály abecedně
                const sortedMaterials = Object.keys(materialGroups).sort();

                // Proměnné pro celkové součty
                let totalCZ21 = 0;
                let totalCZ23 = 0;
                let totalSK11 = 0;
                let totalRequired = 0;
                let availableCZ21 = 0;
                let availableCZ23 = 0;
                let availableSK11 = 0;

                // Zpracování dat o dostupnosti pro každou skupinu materiálů
                for (const baseMaterial of sortedMaterials) {
                    const group = materialGroups[baseMaterial];
                    let totalCZ21Area = 0;
                    let totalCZ23Area = 0;
                    let totalSK11Area = 0;
                    let totalRequiredArea = 0;

                    // Sečteme plochy pro všechny produkty ve skupině
                    for (const { product, info } of group.products) {
                        // Získání množství na skladě pro každou lokaci
                        const cz21Quantity = info.locations && info.locations.A ? info.locations.A.quantity || 0 : 0;
                        const cz23Quantity = info.locations && info.locations.B ? info.locations.B.quantity || 0 : 0;
                        const sk11Quantity = info.locations && info.locations.D ? info.locations.D.quantity || 0 : 0;

                        // Výpočet požadované plochy na základě rozměrů a množství
                        const requiredWidth = info.required_width || 0;
                        const requiredLength = info.required_length || 0;
                        const requiredQuantity = info.required_quantity || 0;

                        // Použití předem vypočítané plochy, pokud je k dispozici
                        const requiredArea = info.required_area || parseFloat(((requiredWidth * requiredLength * requiredQuantity) / 1000000).toFixed(6));

                        // Podrobné logování výpočtu požadované plochy
                        console.log(`Požadovaná plocha pro ${product}:`);
                        if (info.required_area) {
                            console.log(`  Předem vypočítaná plocha: ${info.required_area} m²`);
                        } else {
                            console.log(`  Šířka: ${requiredWidth} mm`);
                            console.log(`  Délka: ${requiredLength} mm`);
                            console.log(`  Množství: ${requiredQuantity}`);
                            console.log(`  Plocha: (${requiredWidth} * ${requiredLength} * ${requiredQuantity}) / 1000000 = ${requiredArea} m²`);
                        }

                        // Použití předem vypočítané plochy pro každou lokaci
                        let cz21Area = 0;
                        if (info.locations && info.locations.A) {
                            // Použití předem vypočítané plochy, pokud je k dispozici
                            cz21Area = info.locations.A.area || 0;
                            console.log(`  Plocha CZ21: ${cz21Area} m²`);
                        }

                        let cz23Area = 0;
                        if (info.locations && info.locations.B) {
                            // Použití předem vypočítané plochy, pokud je k dispozici
                            cz23Area = info.locations.B.area || 0;
                            console.log(`  Plocha CZ23: ${cz23Area} m²`);
                        }

                        let sk11Area = 0;
                        if (info.locations && info.locations.D) {
                            // Použití předem vypočítané plochy, pokud je k dispozici
                            sk11Area = info.locations.D.area || 0;
                            console.log(`  Plocha SK11: ${sk11Area} m²`);
                        }

                        // Přidáme plochy k celkovým plochám pro skupinu
                        totalCZ21Area += cz21Area;
                        totalCZ23Area += cz23Area;
                        totalSK11Area += sk11Area;
                        totalRequiredArea += requiredArea;
                    }

                    // Uložíme celkové plochy do skupiny
                    group.cz21Area = totalCZ21Area;
                    group.cz23Area = totalCZ23Area;
                    group.sk11Area = totalSK11Area;
                    group.requiredArea = totalRequiredArea;

                    // Určení dostupnosti s podporou rezervovaných karet
                    const cz21Status = determineAvailabilityStatus(baseMaterial, 'CZ21', group.cz21Area, group.requiredArea, results);
                    const cz23Status = determineAvailabilityStatus(baseMaterial, 'CZ23', group.cz23Area, group.requiredArea, results);
                    const sk11Status = determineAvailabilityStatus(baseMaterial, 'SK11', group.sk11Area, group.requiredArea, results);

                    const cz21Available = cz21Status.available;
                    const cz23Available = cz23Status.available;
                    const sk11Available = sk11Status.available;

                    // Aktualizace celkových součtů
                    totalCZ21 += group.cz21Area;
                    totalCZ23 += group.cz23Area;
                    totalSK11 += group.sk11Area;
                    totalRequired += group.requiredArea;
                    if (cz21Available) availableCZ21++;
                    if (cz23Available) availableCZ23++;
                    if (sk11Available) availableSK11++;

                    // Formátování čísel s čárkou jako desetinným oddělovačem - přesnější formátování
                    const formatNumber = (num) => num.toFixed(6).replace('.', ',');

                    // Přidání řádku do tabulky s lepším formátováním
                    html += `
                        <tr>
                            <td title="${baseMaterial}">${baseMaterial}</td>
                            <td title="${formatNumber(group.cz21Area)} m²">${formatNumber(group.cz21Area)} m²</td>
                            <td title="${formatNumber(group.cz23Area)} m²">${formatNumber(group.cz23Area)} m²</td>
                            <td title="${formatNumber(group.sk11Area)} m²">${formatNumber(group.sk11Area)} m²</td>
                            <td title="${formatNumber(group.requiredArea)} m²">${formatNumber(group.requiredArea)} m²</td>
                            <td class="${cz21Status.cssClass}" title="${cz21Status.tooltip}">${cz21Status.symbol}</td>
                            <td class="${cz23Status.cssClass}" title="${cz23Status.tooltip}">${cz23Status.symbol}</td>
                            <td class="${sk11Status.cssClass}" title="${sk11Status.tooltip}">${sk11Status.symbol}</td>
                        </tr>
                    `;
                }

                // Přidání řádku s celkovými součty s lepším formátováním
                html += `
                    <tr style="background-color: #f0f0f0; font-weight: bold;">
                        <td title="Celkem">Celkem</td>
                        <td title="${totalCZ21.toFixed(6).replace('.', ',')} m²">${totalCZ21.toFixed(6).replace('.', ',')} m²</td>
                        <td title="${totalCZ23.toFixed(6).replace('.', ',')} m²">${totalCZ23.toFixed(6).replace('.', ',')} m²</td>
                        <td title="${totalSK11.toFixed(6).replace('.', ',')} m²">${totalSK11.toFixed(6).replace('.', ',')} m²</td>
                        <td title="${totalRequired.toFixed(6).replace('.', ',')} m²">${totalRequired.toFixed(6).replace('.', ',')} m²</td>
                        <td title="${availableCZ21} položek">${availableCZ21}</td>
                        <td title="${availableCZ23} položek">${availableCZ23}</td>
                        <td title="${availableSK11} položek">${availableSK11}</td>
                    </tr>
                </table>
                `;

                // Přidání informací o vzdálenostech a optimálním místě výroby
                html += `
                    <div style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;">
                        <h3>Nastavení zpracování:</h3>
                        <ul>
                            <li>Prořez: ✅ Počítáno s 6mm prořezem na všech stranách</li>
                            <li>Výpočet dostupnosti: ✅ Používá se "Mn. na skladě" (celkové množství)</li>
                            <li>Logika rozhodování:</li>
                            <ul>
                                <li>✅ = Mn. na skladě >= požadované množství</li>
                                <li>R = Dostupné mn. = 0 a Rezervované mn. > 0 a pokryje požadavek</li>
                                <li>❌ = Materiál není dostatečně dostupný</li>
                            </ul>
                            <li>Filtrování karet: ✅ Vyloučeny karty s ID řízení dostupnosti "M" (kromě rezervovaných)</li>
                            <li>Vyloučené země: ${excludedCountries.length > 0 ? excludedCountries.join(', ') : 'Žádné'}</li>
                        </ul>

                        <h3>Vzdálenosti od zákazníka:</h3>
                        <ul>
                            <li>CZ21: ${results.optimal_location.distances.CZ21.toFixed(1).replace('.', ',')} km</li>
                            <li>CZ23: ${results.optimal_location.distances.CZ23.toFixed(1).replace('.', ',')} km</li>
                            <li>SK11: ${results.optimal_location.distances.SK11 ? (typeof results.optimal_location.distances.SK11 === 'number' ? results.optimal_location.distances.SK11.toFixed(1).replace('.', ',') + ' km' : results.optimal_location.distances.SK11) : 'N/A'}</li>
                        </ul>

                        <h3>Optimální místo výroby:</h3>
                        <p style="color: #27ae60;">Doporučené místo výroby: <strong>${results.optimal_location.location}</strong></p>
                        <p>Důvod: ${results.optimal_location.reason}</p>
                    </div>
                `;

                // Zobrazení výsledků
                resultsContent.innerHTML = html;

                // Zobrazení tlačítka pro export do CSV
                exportCsvButton.classList.remove('hidden');
            }

            // Funkce pro zobrazení výsledku
            function showResult(element, message, type) {
                element.innerHTML = message;
                element.className = `result-box ${type}`;
                element.classList.remove('hidden');
            }

            // Funkce pro zpracování dat skladu
            function processWarehouseData(data) {
                // Resetování zpracovaných dat
                processedWarehouseData.materialsByBaseName = {};
                processedWarehouseData.materialsByExactName = {};

                // Detekce názvů sloupců
                const columnNames = data.length > 0 ? Object.keys(data[0]) : [];
                console.log('Detekované sloupce v souboru skladu:', columnNames);

                // Mapování názvů sloupců na standardní názvy
                const columnMapping = {
                    description: findColumnName(columnNames, [
                        'Popis položky', 'popis položky', 'POPIS POLOŽKY',
                        'Popis polozky', 'popis polozky', 'POPIS POLOZKY',
                        'Č. položky', 'č. položky', 'ČÍSLO POLOŽKY',
                        'Cislo polozky', 'cislo polozky', 'CISLO POLOZKY',
                        'Název', 'název', 'NÁZEV',
                        'Name', 'name', 'NAME',
                        'Materiál', 'materiál', 'MATERIÁL',
                        'Material', 'material', 'MATERIAL',
                        'Popis', 'popis', 'POPIS',
                        'Description', 'description', 'DESCRIPTION'
                    ]),
                    quantity: findColumnName(columnNames, [
                        'Mn. na skladě', 'mn. na skladě', 'MN. NA SKLADĚ',
                        'Množství na skladě', 'množství na skladě', 'MNOŽSTVÍ NA SKLADĚ',
                        'Mnozstvi na sklade', 'mnozstvi na sklade', 'MNOZSTVI NA SKLADE',
                        'Quantity', 'quantity', 'QUANTITY',
                        'Množství', 'množství', 'MNOŽSTVÍ',
                        'Mnozstvi', 'mnozstvi', 'MNOZSTVI',
                        'Počet', 'počet', 'POČET',
                        'Pocet', 'pocet', 'POCET',
                        'Ks', 'ks', 'KS',
                        'Kusy', 'kusy', 'KUSY'
                    ]),
                    availableQuantity: findColumnName(columnNames, [
                        'Dostupné mn.', 'dostupné mn.', 'DOSTUPNÉ MN.',
                        'Dostupné množství', 'dostupné množství', 'DOSTUPNÉ MNOŽSTVÍ',
                        'Dostupne mnozstvi', 'dostupne mnozstvi', 'DOSTUPNE MNOZSTVI',
                        'Available quantity', 'available quantity', 'AVAILABLE QUANTITY'
                    ]),
                    width: findColumnName(columnNames, [
                        'Šířka (mm)', 'šířka (mm)', 'ŠÍŘKA (MM)',
                        'Sirka (mm)', 'sirka (mm)', 'SIRKA (MM)',
                        'Šířka', 'šířka', 'ŠÍŘKA',
                        'Sirka', 'sirka', 'SIRKA',
                        'Width', 'width', 'WIDTH',
                        'Width (mm)', 'width (mm)', 'WIDTH (MM)'
                    ]),
                    length: findColumnName(columnNames, [
                        'Délka (mm)', 'délka (mm)', 'DÉLKA (MM)',
                        'Delka (mm)', 'delka (mm)', 'DELKA (MM)',
                        'Délka', 'délka', 'DÉLKA',
                        'Delka', 'delka', 'DELKA',
                        'Length', 'length', 'LENGTH',
                        'Length (mm)', 'length (mm)', 'LENGTH (MM)'
                    ]),
                    thickness: findColumnName(columnNames, [
                        'Tloušťka (mm)', 'tloušťka (mm)', 'TLOUŠŤKA (MM)',
                        'Tloustka (mm)', 'tloustka (mm)', 'TLOUSTKA (MM)',
                        'Tloušťka', 'tloušťka', 'TLOUŠŤKA',
                        'Tloustka', 'tloustka', 'TLOUSTKA',
                        'Thickness', 'thickness', 'THICKNESS',
                        'Thickness (mm)', 'thickness (mm)', 'THICKNESS (MM)'
                    ]),
                    location: findColumnName(columnNames, [
                        'Místo', 'místo', 'MÍSTO',
                        'Misto', 'misto', 'MISTO',
                        'Lokace', 'lokace', 'LOKACE',
                        'Location', 'location', 'LOCATION',
                        'Sklad', 'sklad', 'SKLAD',
                        'Warehouse', 'warehouse', 'WAREHOUSE'
                    ]),
                    country: findColumnName(columnNames, [
                        'Země původu', 'země původu', 'ZEMĚ PŮVODU',
                        'Zeme puvodu', 'zeme puvodu', 'ZEME PUVODU',
                        'Země', 'země', 'ZEMĚ',
                        'Zeme', 'zeme', 'ZEME',
                        'Country', 'country', 'COUNTRY',
                        'Country of origin', 'country of origin', 'COUNTRY OF ORIGIN',
                        'Origin', 'origin', 'ORIGIN'
                    ]),
                    reserved: findColumnName(columnNames, [
                        'Rezervované mn.', 'rezervované mn.', 'REZERVOVANÉ MN.',
                        'Rezervovane mn.', 'rezervovane mn.', 'REZERVOVANE MN.',
                        'Rezervované', 'rezervované', 'REZERVOVANÉ',
                        'Rezervovane', 'rezervovane', 'REZERVOVANE',
                        'Rezervováno', 'rezervováno', 'REZERVOVÁNO',
                        'Rezervovano', 'rezervovano', 'REZERVOVANO',
                        'Reserved', 'reserved', 'RESERVED',
                        'Status', 'status', 'STATUS',
                        'Stav', 'stav', 'STAV'
                    ]),
                    availabilityControl: findColumnName(columnNames, [
                        'ID řízení dostupnosti', 'id řízení dostupnosti', 'ID ŘÍZENÍ DOSTUPNOSTI',
                        'ID rizeni dostupnosti', 'id rizeni dostupnosti', 'ID RIZENI DOSTUPNOSTI',
                        'Řízení dostupnosti', 'řízení dostupnosti', 'ŘÍZENÍ DOSTUPNOSTI',
                        'Rizeni dostupnosti', 'rizeni dostupnosti', 'RIZENI DOSTUPNOSTI',
                        'Availability Control', 'availability control', 'AVAILABILITY CONTROL',
                        'Control ID', 'control id', 'CONTROL ID'
                    ])
                };

                console.log('Mapování sloupců:', columnMapping);

                // Zpracování každé položky
                console.log(`Zpracovávám ${data.length} položek ze skladu...`);
                let processedCount = 0;
                let skippedCount = 0;

                for (const item of data) {
                    // Získání hodnot z položky
                    const description = item[columnMapping.description] || '';
                    const quantity = parseFloat((item[columnMapping.quantity] || '0').toString().replace(',', '.')) || 0;

                    const width = parseFloat((item[columnMapping.width] || '0').toString().replace(',', '.')) || 0;
                    const length = parseFloat((item[columnMapping.length] || '0').toString().replace(',', '.')) || 0;
                    const thickness = parseFloat((item[columnMapping.thickness] || '0').toString().replace(',', '.')) || 0;
                    const location = item[columnMapping.location] || '';
                    const country = item[columnMapping.country] || '';

                    // Získání rezervovaného množství
                    let reservedQuantity = 0;
                    if (columnMapping.reserved && item[columnMapping.reserved]) {
                        reservedQuantity = parseFloat((item[columnMapping.reserved] || '0').toString().replace(',', '.')) || 0;
                    }

                    // Výpočet dostupného množství po odečtení rezervací
                    let availableQuantity = 0;
                    if (columnMapping.availableQuantity && item[columnMapping.availableQuantity]) {
                        // Pokud máme sloupec "Dostupné mn.", použijeme ho
                        availableQuantity = parseFloat((item[columnMapping.availableQuantity] || '0').toString().replace(',', '.')) || 0;
                    } else {
                        // Jinak vypočítáme dostupné množství jako celkové - rezervované
                        availableQuantity = Math.max(0, quantity - reservedQuantity);
                    }

                    // Kontrola rezervace - položka je rezervovaná, pokud má rezervované množství > 0
                    const isReserved = reservedQuantity > 0;

                    // Kontrola ID řízení dostupnosti - vyloučíme karty s hodnotou "M" pouze pokud nemají rezervované množství
                    let isExcludedByControl = false;
                    if (columnMapping.availabilityControl && item[columnMapping.availabilityControl]) {
                        const controlValue = item[columnMapping.availabilityControl].toString().toUpperCase();
                        // Vyloučíme pouze karty s "M", které nemají rezervované množství
                        if (controlValue === 'M' && reservedQuantity === 0) {
                            isExcludedByControl = true;
                            console.log(`Vylučuji kartu s ID řízení dostupnosti "M" bez rezervace: ${description}`);
                        } else if (controlValue === 'M' && reservedQuantity > 0) {
                            console.log(`Zachovávám kartu s ID řízení dostupnosti "M" s rezervací: ${description} (rezervováno: ${reservedQuantity})`);
                        }
                    }

                    // Kontrola, zda máme všechny potřebné údaje a zda není vyloučena
                    if (!description || !location || isExcludedByControl) {
                        console.log(`Přeskakuji položku: popis="${description}", lokace="${location}", vyloučena=${isExcludedByControl}`);
                        skippedCount++;
                        continue; // Přeskočíme položku bez popisu, lokace nebo s ID řízení dostupnosti "M"
                    }

                    // Kontrola, zda má položka nějaké množství (dostupné nebo rezervované)
                    if (availableQuantity === 0 && reservedQuantity === 0) {
                        console.log(`Přeskakuji položku bez množství: "${description}"`);
                        skippedCount++;
                        continue; // Přeskočíme položky bez jakéhokoliv množství
                    }

                    // Výpočet plochy v m² - z celkového množství na skladě (ne jen dostupného)
                    const area = parseFloat(((width * length * quantity) / 1000000).toFixed(6));

                    console.log(`Položka: ${description}`);
                    console.log(`  Lokace: ${location}`);
                    console.log(`  Celkové množství: ${quantity}`);
                    console.log(`  Rezervované množství: ${reservedQuantity}`);
                    console.log(`  Dostupné množství: ${availableQuantity}`);
                    console.log(`  Rozměry: ${width}x${length} mm`);
                    console.log(`  Plocha: ${area} m²`);

                    // Extrakce základního názvu materiálu (např. "ENAW5754H111 25,00" z "ENAW5754H111 25,00x40,0x55,0")
                    let baseMaterial = extractBaseMaterialName(description);

                    // Přidání položky do struktury podle základního názvu
                    if (!processedWarehouseData.materialsByBaseName[baseMaterial]) {
                        processedWarehouseData.materialsByBaseName[baseMaterial] = [];
                    }

                    // Přidání položky do struktury podle přesného názvu
                    if (!processedWarehouseData.materialsByExactName[description]) {
                        processedWarehouseData.materialsByExactName[description] = [];
                    }

                    // Výpočet rezervované plochy v m²
                    const reservedArea = parseFloat(((width * length * reservedQuantity) / 1000000).toFixed(6));

                    // Vytvoření objektu položky
                    const itemObject = {
                        description: description,
                        quantity: quantity,
                        reservedQuantity: reservedQuantity,
                        availableQuantity: availableQuantity,
                        width: width,
                        length: length,
                        thickness: thickness,
                        location: location,
                        country: country,
                        area: area,
                        reservedArea: reservedArea,
                        isReserved: isReserved
                    };

                    // Přidání položky do obou struktur
                    processedWarehouseData.materialsByBaseName[baseMaterial].push(itemObject);
                    processedWarehouseData.materialsByExactName[description].push(itemObject);
                    processedCount++;
                }

                console.log(`Zpracování skladu dokončeno: ${processedCount} položek zpracováno, ${skippedCount} přeskočeno`);
                console.log('Zpracovaná data skladu:', processedWarehouseData);
                console.log('Materiály podle základního názvu:', Object.keys(processedWarehouseData.materialsByBaseName));
            }

            // Funkce pro nalezení názvu sloupce v seznamu možných názvů
            function findColumnName(columnNames, possibleNames) {
                for (const name of possibleNames) {
                    if (columnNames.includes(name)) {
                        return name;
                    }
                }
                return null;
            }

            // Funkce pro extrakci základního názvu materiálu
            function extractBaseMaterialName(productName) {
                console.log(`Extrakce základního názvu z: "${productName}"`);

                // Zkusíme najít základní název pomocí regulárního výrazu
                // Upravený regex, který lépe odpovídá formátu "5083 litá 20,00"
                const regex1 = /^(.*?\s\d+,\d+)/;
                const regex2 = /^(.*?\s\d+)/;

                let baseMaterial = productName;
                let match = productName.match(regex1);

                if (match) {
                    baseMaterial = match[1];
                    console.log(`  Regex1 match: "${baseMaterial}"`);
                } else {
                    // Zkusíme alternativní regex pro formát "5083 litá 20"
                    match = productName.match(regex2);
                    if (match) {
                        baseMaterial = match[1];
                        console.log(`  Regex2 match: "${baseMaterial}"`);
                    } else {
                        console.log(`  Žádný regex match, používám celý název: "${baseMaterial}"`);
                    }
                }

                // Normalizace názvu materiálu - odstranění mezer na konci a přidání ",00" pokud chybí
                baseMaterial = baseMaterial.trim();
                if (baseMaterial.match(/\s\d+$/) && !baseMaterial.includes(',')) {
                    baseMaterial = baseMaterial + ',00';
                    console.log(`  Přidáno ,00: "${baseMaterial}"`);
                }

                console.log(`  Finální základní materiál: "${baseMaterial}"`);
                return baseMaterial;
            }

            // Funkce pro zobrazení vyskakovacího okna a získání hodnoty od uživatele
            function promptForValue(message) {
                let value = null;

                // Zobrazení vyskakovacího okna
                while (!value) {
                    const input = prompt(message);

                    // Pokud uživatel klikne na "Zrušit", vrátíme null
                    if (input === null) {
                        return null;
                    }

                    // Pokud uživatel zadal hodnotu, převedeme ji na číslo
                    if (input.trim() !== '') {
                        // Nahradíme čárku tečkou pro správný převod na číslo
                        value = parseFloat(input.replace(',', '.'));

                        // Pokud je hodnota platné číslo, vrátíme ji
                        if (!isNaN(value)) {
                            return value;
                        }
                    }

                    // Pokud hodnota není platné číslo, zobrazíme chybovou zprávu
                    alert('Zadejte prosím platné číslo.');
                }

                return value;
            }

            // Export do CSV
            exportCsvButton.addEventListener('click', function() {
                // Vytvoření CSV obsahu
                let csvContent = 'Materiál;Množství na skladě CZ21;Množství na skladě CZ23;Požadované množství;CZ21;CZ23\n';

                // Získání dat z tabulky
                const table = resultsContent.querySelector('table');
                const rows = table.querySelectorAll('tr');

                // Přeskočení hlavičky (první řádek)
                for (let i = 1; i < rows.length; i++) {
                    const cells = rows[i].querySelectorAll('td');
                    const rowData = [];

                    cells.forEach(cell => {
                        // Odstranění HTML tagů a získání čistého textu
                        const div = document.createElement('div');
                        div.innerHTML = cell.innerHTML;
                        rowData.push(div.textContent.trim());
                    });

                    csvContent += rowData.join(';') + '\n';
                }

                // Vytvoření Blob a stažení souboru
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', 'export_' + new Date().toISOString().slice(0, 10) + '.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });

            // Funkce pro načtení dat vzdáleností
            function loadDistancesData() {
                console.log('🔄 Načítám data vzdáleností z API...');

                // Načtení dat přes PHP API
                fetch('./distances_api.php')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(result => {
                        if (result.success) {
                            distancesData = result.data;

                            // Přidání dat z localStorage
                            const localStorageData = loadFromLocalStorage();
                            for (const localRecord of localStorageData) {
                                // Kontrola, zda PSČ už není v datech ze serveru
                                const exists = distancesData.find(record => record.PSC === localRecord.PSC);
                                if (!exists) {
                                    distancesData.push(localRecord);
                                }
                            }

                            console.log(`✅ Načteno ${result.count} záznamů z API + ${localStorageData.length} z localStorage`);
                            console.log('📋 Ukázka dat:', distancesData.slice(0, 3));
                        } else {
                            throw new Error(result.error || 'Neznámá chyba API');
                        }
                    })
                    .catch(error => {
                        console.warn('⚠️ Nepodařilo se načíst data z API:', error);
                        console.log('🔄 Zkouším fallback načtení z CSV souboru...');

                        // Fallback na přímé načtení CSV
                        loadDistancesDataFromCSV();
                    });
            }

            // Fallback funkce pro načtení z CSV souboru
            function loadDistancesDataFromCSV() {
                const timestamp = new Date().getTime();
                fetch(`./distances.csv?t=${timestamp}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Nepodařilo se načíst soubor distances.csv');
                        }
                        return response.text();
                    })
                    .then(csvContent => {
                        // Zpracování CSV pomocí SheetJS
                        const workbook = XLSX.read(csvContent, {
                            type: 'string',
                            raw: true,
                            FS: ',' // Čárka jako oddělovač
                        });

                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        distancesData = XLSX.utils.sheet_to_json(worksheet);

                        // Přidání dat z localStorage
                        const localStorageData = loadFromLocalStorage();
                        for (const localRecord of localStorageData) {
                            // Kontrola, zda PSČ už není v datech z CSV
                            const exists = distancesData.find(record => record.PSC === localRecord.PSC);
                            if (!exists) {
                                distancesData.push(localRecord);
                            }
                        }

                        console.log(`✅ Načteno ${distancesData.length - localStorageData.length} záznamů z CSV + ${localStorageData.length} z localStorage`);
                        console.log('📋 Ukázka dat:', distancesData.slice(0, 3));
                    })
                    .catch(error => {
                        console.warn('❌ Nepodařilo se načíst data vzdáleností:', error);
                        console.log('Aplikace bude fungovat s automatickým výpočtem vzdáleností');
                        distancesData = [];
                    });
            }

            // Funkce pro výpočet vzdáleností podle PSČ - PRIORITA: API → CSV fallback
            async function calculateDistances(postalCode, options = {}) {
                const { useAPI = true, fallbackToCache = true } = options;
                console.log(`🔍 Počítám vzdálenosti pro PSČ: ${postalCode}`);

                // Input validation
                if (!postalCode || typeof postalCode !== 'string') {
                    throw new Error('Neplatné PSČ');
                }

                const normalizedPostalCode = postalCode.trim();

                // 1. PRIMÁRNÍ: Google Maps API (nejpřesnější data)
                if (useAPI) {
                    console.log('🌐 Používám Google Maps API pro přesný výpočet...');
                    
                    try {
                        const apiDistances = await findDistancesViaAPI(normalizedPostalCode);
                        if (apiDistances && typeof apiDistances === 'object') {
                            console.log('✅ Vzdálenosti vypočítány pomocí Google Maps API:', apiDistances);
                            
                            // Validate API response
                            const validatedDistances = {
                                'CZ21': parseInt(apiDistances.CZ21) || 0,
                                'CZ23': parseInt(apiDistances.CZ23) || 0,
                                'SK11': apiDistances.SK11 || 'N/A',
                                source: 'google_api',
                                city: apiDistances.city || 'API geocoded'
                            };
                            
                            return validatedDistances;
                        }
                    } catch (error) {
                        console.warn('⚠️ Google Maps API selhalo, zkouším CSV fallback:', error);
                    }
                }

                // 2. SEKUNDÁRNÍ: Fallback na CSV data (pouze pokud API selže)
                if (fallbackToCache) {
                    console.log(`💾 API nedostupné, hledám v CSV datech (${distancesData.length} záznamů)...`);

                    // Přesné PSČ v tabulce
                    const exactMatch = distancesData.find(row => row.PSC == normalizedPostalCode);
                    if (exactMatch) {
                        console.log('✅ Nalezeno přesné PSČ v CSV datech (fallback):', exactMatch);
                        return {
                            'CZ21': parseInt(exactMatch.Vzdalenost_CZ21) || 0,
                            'CZ23': parseInt(exactMatch.Vzdalenost_CZ23) || 0,
                            'SK11': exactMatch.Vzdalenost_SK11 || 'N/A',
                            source: 'csv_fallback',
                            city: exactMatch.Mesto || 'Neznámé město'
                        };
                    }
                }

                // 3. POSLEDNÍ MOŽNOST: Žádná data nenalezena
                console.error('❌ PSČ nebylo nalezeno ani v API ani v CSV datech');
                return {
                    'CZ21': 'N/A',
                    'CZ23': 'N/A',
                    'SK11': 'N/A',
                    source: 'not_found',
                    city: 'Nenalezeno'
                }
            }

            // Funkce pro získání průměrných vzdáleností podle regionu
            function getRegionDistances(regionCode) {
                const regionMap = {
                    '10': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '11': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '12': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '13': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '14': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '15': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '16': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '17': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '18': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '19': { CZ21: 150, CZ23: 180, SK11: 280, name: 'Praha' },
                    '20': { CZ21: 130, CZ23: 190, SK11: 290, name: 'Středočeský' },
                    '25': { CZ21: 140, CZ23: 190, SK11: 300, name: 'Středočeský' },
                    '26': { CZ21: 160, CZ23: 170, SK11: 270, name: 'Středočeský' },
                    '27': { CZ21: 130, CZ23: 180, SK11: 290, name: 'Středočeský' },
                    '28': { CZ21: 140, CZ23: 160, SK11: 260, name: 'Středočeský' },
                    '29': { CZ21: 120, CZ23: 150, SK11: 250, name: 'Středočeský' },
                    '30': { CZ21: 50, CZ23: 180, SK11: 320, name: 'Jihočeský' },
                    '31': { CZ21: 40, CZ23: 170, SK11: 310, name: 'Jihočeský' },
                    '32': { CZ21: 60, CZ23: 200, SK11: 340, name: 'Jihočeský' },
                    '33': { CZ21: 80, CZ23: 140, SK11: 280, name: 'Jihočeský' },
                    '34': { CZ21: 90, CZ23: 130, SK11: 270, name: 'Jihočeský' },
                    '35': { CZ21: 80, CZ23: 220, SK11: 360, name: 'Jihočeský/Plzeňský' },
                    '36': { CZ21: 50, CZ23: 190, SK11: 330, name: 'Jihočeský' },
                    '37': { CZ21: 15, CZ23: 165, SK11: 305, name: 'Jihočeský' },
                    '38': { CZ21: 70, CZ23: 210, SK11: 350, name: 'Jihočeský' },
                    '39': { CZ21: 110, CZ23: 120, SK11: 220, name: 'Jihočeský/Vysočina' },
                    '40': { CZ21: 280, CZ23: 180, SK11: 120, name: 'Ústecký (Lovosice, Litoměřice)' },
                    '41': { CZ21: 300, CZ23: 200, SK11: 100, name: 'Ústecký (Lovosice, Litoměřice)' },
                    '42': { CZ21: 290, CZ23: 190, SK11: 110, name: 'Ústecký' },
                    '43': { CZ21: 270, CZ23: 170, SK11: 130, name: 'Ústecký' },
                    '50': { CZ21: 120, CZ23: 250, SK11: 390, name: 'Plzeňský' },
                    '51': { CZ21: 200, CZ23: 250, SK11: 150, name: 'Ústecký' },
                    '60': { CZ21: 165, CZ23: 25, SK11: 165, name: 'Jihomoravský' },
                    '61': { CZ21: 155, CZ23: 35, SK11: 175, name: 'Jihomoravský' },
                    '62': { CZ21: 175, CZ23: 45, SK11: 155, name: 'Jihomoravský' },
                    '63': { CZ21: 180, CZ23: 80, SK11: 120, name: 'Jihomoravský' },
                    '64': { CZ21: 190, CZ23: 70, SK11: 130, name: 'Jihomoravský' },
                    '65': { CZ21: 200, CZ23: 60, SK11: 140, name: 'Jihomoravský' },
                    '66': { CZ21: 185, CZ23: 55, SK11: 145, name: 'Jihomoravský' },
                    '67': { CZ21: 170, CZ23: 70, SK11: 130, name: 'Jihomoravský' },
                    '68': { CZ21: 180, CZ23: 80, SK11: 120, name: 'Jihomoravský' },
                    '69': { CZ21: 195, CZ23: 65, SK11: 135, name: 'Jihomoravský' },
                    '70': { CZ21: 220, CZ23: 120, SK11: 80, name: 'Moravskoslezský' },
                    '71': { CZ21: 230, CZ23: 130, SK11: 70, name: 'Moravskoslezský' },
                    '72': { CZ21: 210, CZ23: 110, SK11: 90, name: 'Moravskoslezský' },
                    '73': { CZ21: 215, CZ23: 115, SK11: 85, name: 'Moravskoslezský' },
                    '74': { CZ21: 200, CZ23: 100, SK11: 100, name: 'Moravskoslezský' },
                    '75': { CZ21: 225, CZ23: 125, SK11: 75, name: 'Moravskoslezský' },
                    '76': { CZ21: 225, CZ23: 125, SK11: 75, name: 'Moravskoslezský' },
                    '77': { CZ21: 235, CZ23: 135, SK11: 65, name: 'Moravskoslezský' },
                    '78': { CZ21: 180, CZ23: 90, SK11: 110, name: 'Olomoucký' },
                    '79': { CZ21: 0, CZ23: 165, SK11: 205, name: 'Moravskoslezský (Bruntál)' }
                };

                return regionMap[regionCode] || null;
            }

            // Funkce pro automatické vyhledání vzdáleností pomocí API s fallbackem
            async function findDistancesViaAPI(postalCode) {
                console.log(`Hledám silniční vzdálenosti pro PSČ ${postalCode}...`);

                try {
                    // 1. Nejprve získáme souřadnice pro zadané PSČ
                    const customerCoords = await getCoordinatesFromPostalCode(postalCode);
                    if (!customerCoords) {
                        throw new Error('Nepodařilo se najít souřadnice pro PSČ');
                    }

                    console.log(`Souřadnice zákazníka (${postalCode}):`, customerCoords);
                    console.log(`Geocoding výsledek: lat=${customerCoords.lat}, lng=${customerCoords.lng}`);

                    // 2. Přesné souřadnice skladů
                    const warehouseCoords = {
                        CZ21: { lat: 49.9888, lng: 17.4578 }, // Bruntál
                        CZ23: { lat: 48.9744, lng: 14.4743 }, // České Budějovice
                        SK11: { lat: 48.2906, lng: 17.7316 }  // Sereď
                    };

                    // 3. Pokus o získání silničních vzdáleností pomocí OpenRouteService
                    console.log('Zkouším OpenRouteService API...');
                    console.log('Počítám vzdálenost do CZ21 (Bruntál)...');
                    let cz21Distance = await getRoadDistance(customerCoords, warehouseCoords.CZ21);
                    console.log('Počítám vzdálenost do CZ23 (České Budějovice)...');
                    let cz23Distance = await getRoadDistance(customerCoords, warehouseCoords.CZ23);
                    console.log('Počítám vzdálenost do SK11 (Sereď, Slovensko)...');
                    let sk11Distance = await getRoadDistance(customerCoords, warehouseCoords.SK11);
                    
                    console.log(`API výsledky: CZ21=${cz21Distance}km, CZ23=${cz23Distance}km, SK11=${sk11Distance}km`);

                    // 4. Pokud API selhalo, použijeme vzdušnou vzdálenost s koeficientem
                    if (cz21Distance === null || cz23Distance === null || sk11Distance === null) {
                        console.log('OpenRouteService API selhalo, používám vzdušnou vzdálenost s koeficientem...');

                        // Výpočet vzdušné vzdálenosti
                        const airDistanceCZ21 = calculateHaversineDistance(
                            customerCoords.lat, customerCoords.lng,
                            warehouseCoords.CZ21.lat, warehouseCoords.CZ21.lng
                        );

                        const airDistanceCZ23 = calculateHaversineDistance(
                            customerCoords.lat, customerCoords.lng,
                            warehouseCoords.CZ23.lat, warehouseCoords.CZ23.lng
                        );

                        const airDistanceSK11 = calculateHaversineDistance(
                            customerCoords.lat, customerCoords.lng,
                            warehouseCoords.SK11.lat, warehouseCoords.SK11.lng
                        );

                        // Koeficient pro převod vzdušné na silniční vzdálenost (1.3x je realistický)
                        const roadCoefficient = 1.3;

                        cz21Distance = Math.round(airDistanceCZ21 * roadCoefficient);
                        cz23Distance = Math.round(airDistanceCZ23 * roadCoefficient);
                        sk11Distance = Math.round(airDistanceSK11 * roadCoefficient);

                        console.log(`Vzdušné vzdálenosti: CZ21=${Math.round(airDistanceCZ21)}km, CZ23=${Math.round(airDistanceCZ23)}km, SK11=${Math.round(airDistanceSK11)}km`);
                        console.log(`Silniční vzdálenosti (koef. ${roadCoefficient}): CZ21=${cz21Distance}km, CZ23=${cz23Distance}km, SK11=${sk11Distance}km`);
                    } else {
                        console.log(`Vypočítané silniční vzdálenosti z API: CZ21=${cz21Distance}km, CZ23=${cz23Distance}km, SK11=${sk11Distance}km`);
                    }

                    return {
                        CZ21: cz21Distance,
                        CZ23: cz23Distance,
                        SK11: sk11Distance
                    };

                } catch (error) {
                    console.error('Chyba při automatickém vyhledání vzdáleností:', error);
                    return null;
                }
            }

            // Funkce pro získání silniční vzdálenosti pomocí OpenRouteService API
            async function getRoadDistance(fromCoords, toCoords) {
                try {
                    console.log(`🛣️ Získávám silniční vzdálenost mezi [${fromCoords.lat}, ${fromCoords.lng}] a [${toCoords.lat}, ${toCoords.lng}]`);
                    
                    // OpenRouteService API - zdarma až 2000 requestů/den
                    const apiKey = '5b3ce3597851110001cf6248a1b2c0c8e8b84b8bb8b8e8b8e8b8e8b8'; // Demo klíč
                    const url = `https://api.openrouteservice.org/v2/directions/driving-car?api_key=${apiKey}`;

                    const requestBody = {
                        coordinates: [
                            [fromCoords.lng, fromCoords.lat],
                            [toCoords.lng, toCoords.lat]
                        ],
                        format: 'json'
                    };

                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestBody)
                    });

                    if (!response.ok) {
                        console.warn(`OpenRouteService API error: ${response.status}`);
                        return null;
                    }

                    const data = await response.json();

                    if (data.routes && data.routes.length > 0) {
                        // Vzdálenost je v metrech, převedeme na kilometry
                        const distanceKm = Math.round(data.routes[0].summary.distance / 1000);
                        console.log(`✅ OpenRouteService úspěch: ${distanceKm}km`);
                        return distanceKm;
                    }

                    console.log(`❌ OpenRouteService: Žádné trasy nalezeny`);
                    return null;
                } catch (error) {
                    console.error('Chyba při volání OpenRouteService API:', error);
                    return null;
                }
            }

            // Cache pre API kľúč
            let apiKeyCache = null;
            
            // Funkcia na získanie Google Maps API kľúča z backendu
            async function getGoogleMapsApiKey() {
                if (apiKeyCache) {
                    return apiKeyCache;
                }
                
                try {
                    const response = await fetch('/api/admin/config');
                    if (response.ok) {
                        const data = await response.json();
                        if (data.status === 'success' && data.config.api_available) {
                            apiKeyCache = data.config.google_maps_api_key;
                            console.log('✅ Google Maps API kľúč načítaný z backendu');
                            return apiKeyCache;
                        }
                    }
                } catch (error) {
                    console.warn('⚠️ Chyba pri načítaní API kľúča z backendu:', error);
                }
                
                return null;
            }

            // Funkce pro získání souřadnic z PSČ pomocí Google Maps API s Nominatim fallback
            async function getCoordinatesFromPostalCode(postalCode) {
                try {
                    // Nejprve zkusíme předdefinované souřadnice pro známá města
                    const predefinedCoords = getPredefinedCoordinates(postalCode);
                    if (predefinedCoords) {
                        console.log(`Použity předdefinované souřadnice pro PSČ ${postalCode}:`, predefinedCoords);
                        return predefinedCoords;
                    }

                    // PRIORITA: Google Maps API (nejpřesnější)
                    const googleApiKey = await getGoogleMapsApiKey();
                    
                    if (googleApiKey) {
                        console.log(`🌐 Hledám souřadnice pro PSČ ${postalCode} pomocí Google Maps API...`);
                        
                        const country = postalCode.startsWith('9') ? 'Slovakia' : 'Czech Republic';
                        const formattedPostalCode = postalCode.length === 5 ? 
                            `${postalCode.substr(0, 3)} ${postalCode.substr(3)}` : postalCode;
                        
                        const response = await fetch(
                            `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(formattedPostalCode + ', ' + country)}&key=${googleApiKey}`
                        );
                        
                        if (response.ok) {
                            const data = await response.json();
                            console.log(`Google Maps API odpověď pro PSČ ${postalCode}:`, data);
                            
                            if (data.status === 'OK' && data.results && data.results.length > 0) {
                                const location = data.results[0].geometry.location;
                                const coords = {
                                    lat: location.lat,
                                    lng: location.lng
                                };
                                console.log(`✅ Google Maps: Nalezeny souřadnice: lat=${coords.lat}, lng=${coords.lng}`);
                                console.log(`Místo: ${data.results[0].formatted_address}`);
                                return coords;
                            }
                        }
                        
                        console.warn('⚠️ Google Maps API selhalo, zkouším Nominatim fallback...');
                    }

                    // FALLBACK: Nominatim API (OpenStreetMap)
                    console.log(`🗺️ Hledám souřadnice pro PSČ ${postalCode} pomocí Nominatim API (fallback)...`);
                    const response = await fetch(
                        `https://nominatim.openstreetmap.org/search?format=json&country=CZ&postalcode=${postalCode}&limit=1`,
                        {
                            headers: {
                                'User-Agent': 'WarehouseApp/1.0'
                            }
                        }
                    );

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();
                    console.log(`Nominatim API odpověď pro PSČ ${postalCode}:`, data);

                    if (data && data.length > 0) {
                        const coords = {
                            lat: parseFloat(data[0].lat),
                            lng: parseFloat(data[0].lon)
                        };
                        console.log(`Nalezeny souřadnice z API: lat=${coords.lat}, lng=${coords.lng}`);
                        console.log(`Místo: ${data[0].display_name}`);
                        return coords;
                    }

                    return null;
                } catch (error) {
                    console.error('Chyba při získávání souřadnic:', error);
                    return null;
                }
            }

            // Funkce pro získání předdefinovaných souřadnic pro známá města
            function getPredefinedCoordinates(postalCode) {
                const predefinedCoords = {
                    // Aš - opravené souřadnice
                    '35201': { lat: 50.2236, lng: 12.1947 },
                    '35200': { lat: 50.2236, lng: 12.1947 },
                    // Jihlava
                    '58601': { lat: 49.3961, lng: 15.5911 },
                    '58600': { lat: 49.3961, lng: 15.5911 },
                    // Liberec
                    '46001': { lat: 50.7663, lng: 15.0543 },
                    '46000': { lat: 50.7663, lng: 15.0543 },
                    // Praha
                    '10000': { lat: 50.0755, lng: 14.4378 },
                    '11000': { lat: 50.0755, lng: 14.4378 },
                    '12000': { lat: 50.0755, lng: 14.4378 },
                    '13000': { lat: 50.0755, lng: 14.4378 },
                    '14000': { lat: 50.0755, lng: 14.4378 },
                    '15000': { lat: 50.0755, lng: 14.4378 },
                    // Brno
                    '60200': { lat: 49.1951, lng: 16.6068 },
                    '60000': { lat: 49.1951, lng: 16.6068 },
                    // Ostrava
                    '70200': { lat: 49.8209, lng: 18.2625 },
                    '70000': { lat: 49.8209, lng: 18.2625 },
                    // Plzeň
                    '30100': { lat: 49.7384, lng: 13.3736 },
                    '30000': { lat: 49.7384, lng: 13.3736 },
                    // České Budějovice
                    '37001': { lat: 48.9744, lng: 14.4743 },
                    '37000': { lat: 48.9744, lng: 14.4743 },
                    // Bruntál
                    '79201': { lat: 49.9888, lng: 17.4578 },
                    '79200': { lat: 49.9888, lng: 17.4578 },
                    // Lovosice
                    '41001': { lat: 50.5151, lng: 14.0508 },
                    '41000': { lat: 50.5151, lng: 14.0508 }
                };

                console.log(`Hledám předdefinované souřadnice pro PSČ: ${postalCode}`);
                const result = predefinedCoords[postalCode] || null;
                if (result) {
                    console.log(`✅ Nalezeny předdefinované souřadnice:`, result);
                } else {
                    console.log(`❌ Předdefinované souřadnice nenalezeny pro PSČ ${postalCode}`);
                }
                return result;
            }

            // Funkce pro výpočet vzdálenosti pomocí Haversine formule
            function calculateHaversineDistance(lat1, lng1, lat2, lng2) {
                const R = 6371; // Poloměr Země v km
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLng = (lng2 - lng1) * Math.PI / 180;
                const a =
                    Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLng/2) * Math.sin(dLng/2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                const distance = R * c;
                return distance;
            }

            // Funkce pro tiché uložení nového záznamu vzdálenosti (bez modálního okna)
            async function saveNewDistanceRecordSilently(postalCode, cz21Distance, cz23Distance, sk11Distance = null) {
                console.log(`💾 Automaticky ukládám nový záznam: PSČ ${postalCode}, CZ21: ${cz21Distance}km, CZ23: ${cz23Distance}km, SK11: ${sk11Distance || 'N/A'}km`);

                try {
                    // Uložení přes PHP API
                    const response = await fetch('./distances_api.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            postalCode: postalCode,
                            cz21Distance: cz21Distance,
                            cz23Distance: cz23Distance,
                            sk11Distance: sk11Distance
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        console.log('✅ PSČ bylo úspěšně uloženo na server:', result);

                        // Určení kraje a města podle PSČ pro lokální cache
                        const regionInfo = getRegionInfoByPostalCode(postalCode);

                        // Vytvoření nového záznamu pro lokální cache
                        const newRecord = {
                            PSC: postalCode,
                            Kraj: regionInfo.kraj,
                            Mesto: regionInfo.mesto,
                            Vzdalenost_CZ21: cz21Distance,
                            Vzdalenost_CZ23: cz23Distance,
                            Vzdalenost_SK11: sk11Distance
                        };

                        // Přidání do lokální tabulky
                        distancesData.push(newRecord);

                    } else {
                        throw new Error(result.error || 'Neznámá chyba při ukládání');
                    }

                } catch (error) {
                    console.error('❌ Chyba při ukládání PSČ na server:', error);

                    // Fallback - uložení do localStorage
                    const regionInfo = getRegionInfoByPostalCode(postalCode);
                    const newRecord = {
                        PSC: postalCode,
                        Kraj: regionInfo.kraj,
                        Mesto: regionInfo.mesto,
                        Vzdalenost_CZ21: cz21Distance,
                        Vzdalenost_CZ23: cz23Distance,
                        Vzdalenost_SK11: sk11Distance
                    };

                    // Přidání do lokální tabulky
                    distancesData.push(newRecord);

                    // Uložení do localStorage pro trvalost
                    saveToLocalStorage(newRecord);

                    console.log('💾 PSČ uloženo do localStorage pro trvalé uložení');
                }
            }

            // Funkce pro automatické stažení aktualizovaného CSV souboru
            function downloadUpdatedDistancesCSV() {
                console.log('📥 Generuji aktualizovaný distances.csv soubor...');

                // Seřazení podle PSČ
                const sortedData = [...distancesData].sort((a, b) => parseInt(a.PSC) - parseInt(b.PSC));

                // Vytvoření CSV hlavičky
                let csvContent = 'PSC,Kraj,Mesto,Vzdalenost_CZ21,Vzdalenost_CZ23\n';

                // Přidání všech řádků
                for (const record of sortedData) {
                    csvContent += `${record.PSC},${record.Kraj},${record.Mesto},${record.Vzdalenost_CZ21},${record.Vzdalenost_CZ23}\n`;
                }

                // Stažení souboru
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', 'distances.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                console.log('✅ Aktualizovaný distances.csv byl automaticky stažen');
            }

            // Funkce pro uložení do localStorage
            function saveToLocalStorage(newRecord) {
                try {
                    // Načtení existujících dat z localStorage
                    const existingData = JSON.parse(localStorage.getItem('distancesCache') || '[]');

                    // Kontrola, zda PSČ už neexistuje
                    const existingIndex = existingData.findIndex(record => record.PSC === newRecord.PSC);

                    if (existingIndex >= 0) {
                        // Aktualizace existujícího záznamu
                        existingData[existingIndex] = newRecord;
                    } else {
                        // Přidání nového záznamu
                        existingData.push(newRecord);
                    }

                    // Uložení zpět do localStorage
                    localStorage.setItem('distancesCache', JSON.stringify(existingData));
                    console.log('💾 PSČ uloženo do localStorage:', newRecord);

                } catch (error) {
                    console.error('❌ Chyba při ukládání do localStorage:', error);
                }
            }

            // Funkce pro načtení z localStorage
            function loadFromLocalStorage() {
                try {
                    const cachedData = JSON.parse(localStorage.getItem('distancesCache') || '[]');
                    console.log(`📱 Načteno ${cachedData.length} PSČ z localStorage`);
                    return cachedData;
                } catch (error) {
                    console.error('❌ Chyba při načítání z localStorage:', error);
                    return [];
                }
            }



            // Funkce pro určení stavu dostupnosti s podporou rezervovaných karet
            function determineAvailabilityStatus(baseMaterial, location, availableArea, requiredArea, results) {
                console.log(`🔍 Kontroluji dostupnost: ${baseMaterial} v ${location}`);
                console.log(`  Dostupná plocha: ${availableArea} m²`);
                console.log(`  Požadovaná plocha: ${requiredArea} m²`);

                // Najdeme informace o materiálu v results
                const materialInfo = results.availability[baseMaterial];
                if (!materialInfo || !materialInfo.locations) {
                    console.log(`  Materiál ${baseMaterial} nenalezen v results`);
                    return {
                        available: false,
                        symbol: '❌',
                        cssClass: 'location-unavailable',
                        tooltip: 'Nedostupné'
                    };
                }

                const locationKey = location === 'CZ21' ? 'A' : 'B';
                const locationData = materialInfo.locations[locationKey];

                if (!locationData || !locationData.items || locationData.items.length === 0) {
                    console.log(`  Žádné položky pro ${baseMaterial} v ${location}`);
                    return {
                        available: false,
                        symbol: '❌',
                        cssClass: 'location-unavailable',
                        tooltip: 'Nedostupné'
                    };
                }

                // Výpočet celkové plochy na skladě (z celkového množství)
                let totalAreaInStock = 0;
                let availableAreaInStock = 0;
                let reservedAreaInStock = 0;
                let hasReservedItems = false;

                for (const item of locationData.items) {
                    const itemTotalArea = (item.width * item.length * item.quantity) / 1000000;
                    const itemAvailableArea = (item.width * item.length * item.availableQuantity) / 1000000;
                    const itemReservedArea = (item.width * item.length * item.reservedQuantity) / 1000000;

                    totalAreaInStock += itemTotalArea;
                    availableAreaInStock += itemAvailableArea;
                    reservedAreaInStock += itemReservedArea;

                    if (item.reservedQuantity > 0) {
                        hasReservedItems = true;
                    }

                    console.log(`    Položka: ${item.description}`);
                    console.log(`      Celková plocha: ${itemTotalArea.toFixed(6)} m²`);
                    console.log(`      Dostupná plocha: ${itemAvailableArea.toFixed(6)} m²`);
                    console.log(`      Rezervovaná plocha: ${itemReservedArea.toFixed(6)} m²`);
                }

                console.log(`  Celková plocha na skladě: ${totalAreaInStock.toFixed(6)} m²`);
                console.log(`  Dostupná plocha na skladě: ${availableAreaInStock.toFixed(6)} m²`);
                console.log(`  Rezervovaná plocha na skladě: ${reservedAreaInStock.toFixed(6)} m²`);

                // Logika rozhodování (pořadí je důležité!):
                // 1. R - když dostupná plocha = 0 a rezervovaná plocha > 0 a rezervovaná plocha >= požadovaná plocha
                if (availableAreaInStock === 0 && hasReservedItems && reservedAreaInStock >= requiredArea) {
                    console.log(`  R Pouze rezervované karty pokryjí požadavek`);
                    return {
                        available: false, // Pro logiku optimalizace stále nedostupné
                        symbol: 'R',
                        cssClass: 'location-reserved',
                        tooltip: `Rezervované karty dostupné (${reservedAreaInStock.toFixed(2)} m²)`
                    };
                }

                // 2. ✅ - když celková plocha na skladě >= požadovaná plocha
                if (totalAreaInStock >= requiredArea) {
                    console.log(`  ✅ Celková plocha na skladě pokryje požadavek`);
                    return {
                        available: true,
                        symbol: '✅',
                        cssClass: 'location-available',
                        tooltip: `Dostupné (${totalAreaInStock.toFixed(2)} m² na skladě)`
                    };
                }

                // 3. ❌ - když nic nevyhovuje
                console.log(`  ❌ Materiál není dostatečně dostupný`);
                return {
                    available: false,
                    symbol: '❌',
                    cssClass: 'location-unavailable',
                    tooltip: `Nedostupné (${totalAreaInStock.toFixed(2)} m² na skladě, požadováno ${requiredArea.toFixed(2)} m²)`
                };
            }

            // Funkce pro uložení nového záznamu vzdálenosti (s modálním oknem)
            function saveNewDistanceRecord(postalCode, cz21Distance, cz23Distance) {
                console.log(`Ukládám nový záznam: PSČ ${postalCode}, CZ21: ${cz21Distance}km, CZ23: ${cz23Distance}km`);

                // Určení kraje a města podle PSČ
                const regionInfo = getRegionInfoByPostalCode(postalCode);

                // Vytvoření nového záznamu
                const newRecord = {
                    PSC: postalCode,
                    Kraj: regionInfo.kraj,
                    Mesto: regionInfo.mesto,
                    Vzdalenost_CZ21: cz21Distance,
                    Vzdalenost_CZ23: cz23Distance
                };

                // Přidání do lokální tabulky
                distancesData.push(newRecord);

                // Generování aktualizovaného CSV obsahu
                const updatedCsvContent = generateUpdatedCsvContent();

                // Zobrazení informace uživateli
                showDistanceUpdateInfo(newRecord, updatedCsvContent);

                console.log('Nový záznam byl přidán do lokální tabulky:', newRecord);
            }

            // Funkce pro určení kraje a města podle PSČ
            function getRegionInfoByPostalCode(postalCode) {
                const regionCode = postalCode.substring(0, 2);

                const regionMap = {
                    '10': { kraj: 'Praha', mesto: 'Praha' },
                    '11': { kraj: 'Praha', mesto: 'Praha' },
                    '12': { kraj: 'Praha', mesto: 'Praha' },
                    '13': { kraj: 'Praha', mesto: 'Praha' },
                    '14': { kraj: 'Praha', mesto: 'Praha' },
                    '15': { kraj: 'Praha', mesto: 'Praha' },
                    '16': { kraj: 'Praha', mesto: 'Praha' },
                    '17': { kraj: 'Praha', mesto: 'Praha' },
                    '18': { kraj: 'Praha', mesto: 'Praha' },
                    '19': { kraj: 'Praha', mesto: 'Praha' },
                    '20': { kraj: 'Středočeský', mesto: 'Mladá Boleslav' },
                    '25': { kraj: 'Středočeský', mesto: 'Kladno' },
                    '26': { kraj: 'Středočeský', mesto: 'Beroun' },
                    '27': { kraj: 'Středočeský', mesto: 'Kolín' },
                    '28': { kraj: 'Středočeský', mesto: 'Kutná Hora' },
                    '29': { kraj: 'Středočeský', mesto: 'Benešov' },
                    '30': { kraj: 'Jihočeský', mesto: 'Strakonice' },
                    '31': { kraj: 'Jihočeský', mesto: 'Písek' },
                    '32': { kraj: 'Jihočeský', mesto: 'Prachatice' },
                    '33': { kraj: 'Jihočeský', mesto: 'Jindřichův Hradec' },
                    '34': { kraj: 'Jihočeský', mesto: 'Tábor' },
                    '35': { kraj: 'Plzeňský', mesto: 'Klatovy' },
                    '36': { kraj: 'Jihočeský', mesto: 'Český Krumlov' },
                    '37': { kraj: 'Jihočeský', mesto: 'České Budějovice' },
                    '79': { kraj: 'Moravskoslezský', mesto: 'Bruntál' },
                    '38': { kraj: 'Jihočeský', mesto: 'Vimperk' },
                    '39': { kraj: 'Vysočina', mesto: 'Pelhřimov' },
                    '40': { kraj: 'Karlovarský', mesto: 'Karlovy Vary' },
                    '41': { kraj: 'Karlovarský', mesto: 'Cheb' },
                    '42': { kraj: 'Karlovarský', mesto: 'Sokolov' },
                    '43': { kraj: 'Karlovarský', mesto: 'Mariánské Lázně' },
                    '50': { kraj: 'Plzeňský', mesto: 'Plzeň' },
                    '51': { kraj: 'Ústecký', mesto: 'Ústí nad Labem' },
                    '60': { kraj: 'Jihomoravský', mesto: 'Brno' },
                    '61': { kraj: 'Jihomoravský', mesto: 'Blansko' },
                    '62': { kraj: 'Jihomoravský', mesto: 'Vyškov' },
                    '63': { kraj: 'Jihomoravský', mesto: 'Znojmo' },
                    '64': { kraj: 'Jihomoravský', mesto: 'Břeclav' },
                    '65': { kraj: 'Jihomoravský', mesto: 'Hodonín' },
                    '66': { kraj: 'Jihomoravský', mesto: 'Uherské Hradiště' },
                    '67': { kraj: 'Jihomoravský', mesto: 'Kroměříž' },
                    '68': { kraj: 'Jihomoravský', mesto: 'Zlín' },
                    '69': { kraj: 'Jihomoravský', mesto: 'Veselí nad Moravou' },
                    '70': { kraj: 'Moravskoslezský', mesto: 'Ostrava' },
                    '71': { kraj: 'Moravskoslezský', mesto: 'Karviná' },
                    '72': { kraj: 'Moravskoslezský', mesto: 'Frýdek-Místek' },
                    '73': { kraj: 'Moravskoslezský', mesto: 'Opava' },
                    '74': { kraj: 'Moravskoslezský', mesto: 'Nový Jičín' },
                    '75': { kraj: 'Moravskoslezský', mesto: 'Třinec' },
                    '76': { kraj: 'Moravskoslezský', mesto: 'Havířov' },
                    '77': { kraj: 'Moravskoslezský', mesto: 'Bohumín' },
                    '78': { kraj: 'Olomoucký', mesto: 'Olomouc' },
                    '79': { kraj: 'Olomoucký', mesto: 'Prostějov' }
                };

                return regionMap[regionCode] || { kraj: 'Neznámý', mesto: 'Neznámé' };
            }

            // Funkce pro generování aktualizovaného CSV obsahu
            function generateUpdatedCsvContent() {
                // Seřazení podle PSČ
                const sortedData = [...distancesData].sort((a, b) => parseInt(a.PSC) - parseInt(b.PSC));

                // Vytvoření CSV hlavičky
                let csvContent = 'PSC,Kraj,Mesto,Vzdalenost_CZ21,Vzdalenost_CZ23\n';

                // Přidání všech řádků
                for (const record of sortedData) {
                    csvContent += `${record.PSC},${record.Kraj},${record.Mesto},${record.Vzdalenost_CZ21},${record.Vzdalenost_CZ23}\n`;
                }

                return csvContent;
            }

            // Funkce pro zobrazení informace o aktualizaci
            function showDistanceUpdateInfo(newRecord, csvContent) {
                // Vytvoření modálního okna s informacemi
                const modal = document.createElement('div');
                modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                `;

                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background-color: white;
                    padding: 30px;
                    border-radius: 10px;
                    max-width: 600px;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                `;

                modalContent.innerHTML = `
                    <h3>✅ Nový záznam vzdálenosti byl přidán</h3>
                    <p><strong>PSČ:</strong> ${newRecord.PSC}</p>
                    <p><strong>Kraj:</strong> ${newRecord.Kraj}</p>
                    <p><strong>Město:</strong> ${newRecord.Mesto}</p>
                    <p><strong>Vzdálenost do CZ21:</strong> ${newRecord.Vzdalenost_CZ21} km</p>
                    <p><strong>Vzdálenost do CZ23:</strong> ${newRecord.Vzdalenost_CZ23} km</p>

                    <h4>📋 Aktualizovaný CSV obsah:</h4>
                    <textarea readonly style="width: 100%; height: 200px; font-family: monospace; font-size: 12px;">${csvContent}</textarea>

                    <div style="margin-top: 20px;">
                        <button id="download-csv" style="background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                            💾 Stáhnout aktualizovaný CSV
                        </button>
                        <button id="close-modal" style="background-color: #f44336; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;">
                            ❌ Zavřít
                        </button>
                    </div>

                    <p style="margin-top: 15px; font-size: 12px; color: #666;">
                        💡 <strong>Tip:</strong> Stáhněte si aktualizovaný CSV soubor a nahraďte jím původní distances.csv soubor pro trvalé uložení změn.
                    </p>
                `;

                modal.appendChild(modalContent);
                document.body.appendChild(modal);

                // Obsluha tlačítek
                document.getElementById('download-csv').addEventListener('click', function() {
                    downloadCsvFile(csvContent, `distances_updated_${new Date().toISOString().slice(0, 10)}.csv`);
                });

                document.getElementById('close-modal').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });

                // Zavření na klik mimo modal
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                    }
                });
            }

            // Funkce pro stažení CSV souboru
            function downloadCsvFile(csvContent, filename) {
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        });
    </script>
</body>
</html>
