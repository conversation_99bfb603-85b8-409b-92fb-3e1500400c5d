@echo off
chcp 65001 >nul
echo =========================================
echo   REBUILD MIGRACE_OBJEDNAVEK.EXE v8.1
echo   s ukládáním do složky Dokumenty
echo =========================================
echo.

REM Prejdi do projektove slozky
for /d %%i in ("Migrace - Arno*") do (
    echo Nalezena projektova slozka: %%i
    cd "%%i"
    goto :found
)

echo CHYBA: Projektova slozka nenalezena!
echo Zkontrolujte, ze jste ve spravnem adresari.
pause
exit /b 1

:found
echo Pracovni adresar: %CD%
echo.

REM Kontrola a nahrazeni main.py
echo [1/5] Aktualizuji main.py s Documents podporou...
if exist ..\main_documents_fixed.py (
    copy ..\main_documents_fixed.py main.py >nul
    echo ✓ main.py aktualizovan s Documents podporou
) else (
    echo VAROVANI: main_documents_fixed.py nenalezen
    echo Pokracuji s existujicim main.py
)

REM Kontrola uprav
findstr /C:"get_documents_folder" main.py >nul
if errorlevel 1 (
    echo [2/5] VAROVANI: main.py neobsahuje get_documents_folder funkci
) else (
    echo [2/5] ✓ main.py obsahuje Documents funkcionalitu - OK
)

REM Aktivace virtualniho prostredi
echo [3/5] Aktivuji virtualni prostredi...
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo ✓ Virtualni prostredi aktivovano
) else (
    echo VAROVANI: Virtualni prostredi nenalezeno, pouzivam systemovy Python
)

REM Instalace zavislosti
echo [4/5] Kontroluji zavislosti...
pip show pyinstaller >nul 2>&1 || pip install pyinstaller
pip show pandas >nul 2>&1 || pip install pandas  
pip show openpyxl >nul 2>&1 || pip install openpyxl

REM Build exe
echo [5/5] Builduji exe aplikaci...
if exist main.spec (
    echo Pouzivam existujici main.spec...
    pyinstaller main.spec --clean --noconfirm
) else (
    echo Vytvarim novy build...
    pyinstaller --onefile --windowed --add-data "ALDE_katalog_položek.xlsx;." --name "Migrace_Objednavek" main.py --noconfirm
)

REM Kontrola vysledku
if exist "dist\Migrace_Objednavek.exe" (
    echo.
    echo =========================================
    echo   USPECH!
    echo =========================================
    echo Novy exe soubor: dist\Migrace_Objednavek.exe
    
    REM Kopirovani katalogu
    for %%f in (ALDE_katalog_*.xlsx) do (
        copy "%%f" "dist\" >nul 2>&1
        echo ✓ Katalog %%f zkopirovan do dist\
    )
    
    REM Informace o velikosti
    for %%F in (dist\Migrace_Objednavek.exe) do (
        set /a size=%%~zF/1024/1024
    )
    echo Velikost: %size% MB
    
    echo.
    echo =========================================
    echo   NOVA FUNKCIONALITA v8.1:
    echo =========================================
    echo ✓ Flexibilni prevodni pro materialy
    echo ✓ Podpora CSV i Excel souboru  
    echo ✓ Ukladani do: %%USERPROFILE%%\Documents\Migrace_Vystupy\
    echo ✓ Automaticke vytvareni vystupni slozky
    echo ✓ Cross-platform podpora Documents slozky
    echo.
    echo SPUSTENI: dist\Migrace_Objednavek.exe
    echo.
    echo Obsah dist\:
    dir dist\ /b
    
    echo.
    echo ✓ MIGRACE v8.1 USPESNE SESTAVENA!
    echo Aplikace bude ukladat soubory do vasi slozky Dokumenty.
    
) else (
    echo.
    echo =========================================
    echo   CHYBA PRI BUILDU!
    echo =========================================
    echo Zkontrolujte chybove hlasky vyse.
    if exist build (
        echo Obsah build slozky:
        dir build /s /b
    )
)

echo.
echo Stiskni libovolnou klavesu pro ukonceni...
pause >nul